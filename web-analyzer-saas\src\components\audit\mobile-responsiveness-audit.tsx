"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { 
  Smartphone, 
  CheckCircle,
  XCircle,
  AlertTriangle,
  Search,
  Eye,
  Globe
} from "@/components/ui/icons"

interface MobileIssue {
  type: "error" | "warning" | "info"
  title: string
  description: string
  element?: string
  recommendation: string
}

interface ViewportTest {
  device: string
  width: number
  height: number
  passed: boolean
  issues: string[]
}

interface MobileAuditResult {
  url: string
  overallScore: number
  mobileUsabilityScore: number
  viewportConfigured: boolean
  textReadable: boolean
  tapTargetsAppropriate: boolean
  contentSizedCorrectly: boolean
  issues: MobileIssue[]
  viewportTests: ViewportTest[]
  recommendations: Array<{
    title: string
    description: string
    priority: "high" | "medium" | "low"
    category: string
  }>
}

const mockResults: MobileAuditResult = {
  url: "https://example.com",
  overallScore: 78,
  mobileUsabilityScore: 85,
  viewportConfigured: true,
  textReadable: false,
  tapTargetsAppropriate: true,
  contentSizedCorrectly: false,
  issues: [
    {
      type: "error",
      title: "Text too small to read",
      description: "Some text on the page is too small to read on mobile devices",
      element: "p.small-text",
      recommendation: "Increase font size to at least 16px for body text"
    },
    {
      type: "warning", 
      title: "Content wider than screen",
      description: "Some content extends beyond the viewport width",
      element: "div.wide-content",
      recommendation: "Use responsive design techniques to ensure content fits within viewport"
    },
    {
      type: "info",
      title: "Viewport meta tag found",
      description: "Page has a viewport meta tag configured",
      recommendation: "Good! This helps with mobile rendering"
    }
  ],
  viewportTests: [
    {
      device: "iPhone 12",
      width: 390,
      height: 844,
      passed: true,
      issues: []
    },
    {
      device: "Samsung Galaxy S21",
      width: 384,
      height: 854,
      passed: true,
      issues: []
    },
    {
      device: "iPad",
      width: 768,
      height: 1024,
      passed: false,
      issues: ["Content overflow on landscape orientation"]
    },
    {
      device: "Small Mobile",
      width: 320,
      height: 568,
      passed: false,
      issues: ["Text too small", "Tap targets too close"]
    }
  ],
  recommendations: [
    {
      title: "Implement responsive typography",
      description: "Use relative units (rem, em) and CSS clamp() for scalable text sizes",
      priority: "high",
      category: "Typography"
    },
    {
      title: "Optimize tap target sizes",
      description: "Ensure interactive elements are at least 44px in height and width",
      priority: "medium", 
      category: "Usability"
    },
    {
      title: "Test on real devices",
      description: "Supplement automated testing with manual testing on actual mobile devices",
      priority: "low",
      category: "Testing"
    }
  ]
}

export function MobileResponsivenessAudit() {
  const [url, setUrl] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [results, setResults] = useState<MobileAuditResult | null>(null)

  const handleAudit = async () => {
    if (!url) return
    
    setIsLoading(true)
    setTimeout(() => {
      setResults({ ...mockResults, url })
      setIsLoading(false)
    }, 3000)
  }

  const getScoreColor = (score: number) => {
    if (score >= 90) return "text-green-600"
    if (score >= 70) return "text-yellow-600"
    return "text-red-600"
  }

  const getIssueIcon = (type: string) => {
    switch (type) {
      case "error": return <XCircle className="h-5 w-5 text-red-600" />
      case "warning": return <AlertTriangle className="h-5 w-5 text-yellow-600" />
      case "info": return <CheckCircle className="h-5 w-5 text-blue-600" />
      default: return <AlertTriangle className="h-5 w-5 text-gray-600" />
    }
  }

  return (
    <div className="space-y-6">
      {/* URL Input */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Smartphone className="h-5 w-5" />
            Mobile Responsiveness Test
          </CardTitle>
          <CardDescription>
            Test your website's mobile usability and responsiveness across different devices
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4">
            <input
              type="url"
              placeholder="https://example.com"
              value={url}
              onChange={(e) => setUrl(e.target.value)}
              className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <Button 
              onClick={handleAudit} 
              disabled={!url || isLoading}
              className="min-w-[120px]"
            >
              {isLoading ? "Testing..." : "Test Mobile"}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Loading State */}
      {isLoading && (
        <Card>
          <CardContent className="py-8">
            <div className="text-center space-y-4">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
              <p className="text-gray-600">Testing mobile responsiveness...</p>
              <p className="text-sm text-gray-500">Checking multiple device sizes and orientations</p>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Results */}
      {results && !isLoading && (
        <div className="space-y-6">
          {/* Overall Scores */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Mobile Usability Score</CardTitle>
                <CardDescription>Google Mobile-Friendly Test equivalent</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between">
                  <div className={`text-4xl font-bold ${getScoreColor(results.mobileUsabilityScore)}`}>
                    {results.mobileUsabilityScore}
                  </div>
                  <Smartphone className="h-8 w-8 text-gray-400" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Overall Responsiveness</CardTitle>
                <CardDescription>Comprehensive mobile experience score</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between">
                  <div className={`text-4xl font-bold ${getScoreColor(results.overallScore)}`}>
                    {results.overallScore}
                  </div>
                  <Globe className="h-8 w-8 text-gray-400" />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Quick Checks */}
          <Card>
            <CardHeader>
              <CardTitle>Mobile Usability Checks</CardTitle>
              <CardDescription>Key mobile usability factors</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div className="flex items-center gap-3 p-3 border rounded-lg">
                  {results.viewportConfigured ? 
                    <CheckCircle className="h-5 w-5 text-green-600" /> : 
                    <XCircle className="h-5 w-5 text-red-600" />
                  }
                  <div>
                    <div className="font-medium text-sm">Viewport Configured</div>
                    <div className="text-xs text-gray-600">Meta viewport tag</div>
                  </div>
                </div>

                <div className="flex items-center gap-3 p-3 border rounded-lg">
                  {results.textReadable ? 
                    <CheckCircle className="h-5 w-5 text-green-600" /> : 
                    <XCircle className="h-5 w-5 text-red-600" />
                  }
                  <div>
                    <div className="font-medium text-sm">Text Readable</div>
                    <div className="text-xs text-gray-600">Font size adequate</div>
                  </div>
                </div>

                <div className="flex items-center gap-3 p-3 border rounded-lg">
                  {results.tapTargetsAppropriate ? 
                    <CheckCircle className="h-5 w-5 text-green-600" /> : 
                    <XCircle className="h-5 w-5 text-red-600" />
                  }
                  <div>
                    <div className="font-medium text-sm">Tap Targets</div>
                    <div className="text-xs text-gray-600">Appropriate sizing</div>
                  </div>
                </div>

                <div className="flex items-center gap-3 p-3 border rounded-lg">
                  {results.contentSizedCorrectly ? 
                    <CheckCircle className="h-5 w-5 text-green-600" /> : 
                    <XCircle className="h-5 w-5 text-red-600" />
                  }
                  <div>
                    <div className="font-medium text-sm">Content Sizing</div>
                    <div className="text-xs text-gray-600">Fits viewport</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Device Tests */}
          <Card>
            <CardHeader>
              <CardTitle>Device Compatibility</CardTitle>
              <CardDescription>Test results across different device sizes</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {results.viewportTests.map((test, index) => (
                  <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center gap-4">
                      <div className="flex items-center gap-2">
                        {test.passed ? 
                          <CheckCircle className="h-5 w-5 text-green-600" /> : 
                          <XCircle className="h-5 w-5 text-red-600" />
                        }
                        <div>
                          <div className="font-medium">{test.device}</div>
                          <div className="text-sm text-gray-600">{test.width} × {test.height}px</div>
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      {test.passed ? (
                        <Badge className="bg-green-100 text-green-800">Passed</Badge>
                      ) : (
                        <div className="space-y-1">
                          <Badge variant="destructive">Failed</Badge>
                          {test.issues.map((issue, i) => (
                            <div key={i} className="text-xs text-red-600">{issue}</div>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Issues */}
          <Card>
            <CardHeader>
              <CardTitle>Mobile Usability Issues</CardTitle>
              <CardDescription>Detailed issues found during mobile testing</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {results.issues.map((issue, index) => (
                  <div key={index} className="flex items-start gap-3 p-4 border rounded-lg">
                    {getIssueIcon(issue.type)}
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <h4 className="font-medium">{issue.title}</h4>
                        <Badge 
                          variant={issue.type === "error" ? "destructive" : issue.type === "warning" ? "secondary" : "outline"}
                          className="text-xs"
                        >
                          {issue.type}
                        </Badge>
                      </div>
                      <p className="text-sm text-gray-600 mb-2">{issue.description}</p>
                      {issue.element && (
                        <div className="text-xs text-gray-500 mb-2">
                          Element: <code className="bg-gray-100 px-1 rounded">{issue.element}</code>
                        </div>
                      )}
                      <div className="text-sm text-blue-600">{issue.recommendation}</div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Recommendations */}
          <Card>
            <CardHeader>
              <CardTitle>Optimization Recommendations</CardTitle>
              <CardDescription>Suggestions to improve mobile experience</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {results.recommendations.map((rec, index) => (
                  <div key={index} className="border rounded-lg p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <h4 className="font-medium">{rec.title}</h4>
                          <Badge variant="outline" className="text-xs">{rec.category}</Badge>
                        </div>
                        <p className="text-sm text-gray-600 mb-3">{rec.description}</p>
                        <Badge 
                          variant={rec.priority === "high" ? "destructive" : rec.priority === "medium" ? "secondary" : "outline"}
                          className="text-xs"
                        >
                          {rec.priority} priority
                        </Badge>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  )
}
