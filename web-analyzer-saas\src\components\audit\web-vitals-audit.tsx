"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { 
  Activity, 
  Clock, 
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  Search,
  BarChart3
} from "@/components/ui/icons"

interface VitalMetric {
  name: string
  value: number
  unit: string
  status: "good" | "needs-improvement" | "poor"
  description: string
  threshold: {
    good: number
    poor: number
  }
}

interface WebVitalsResult {
  url: string
  timestamp: string
  metrics: VitalMetric[]
  fieldData: {
    fcp: { p75: number, status: string }
    lcp: { p75: number, status: string }
    cls: { p75: number, status: string }
    fid: { p75: number, status: string }
  }
  recommendations: Array<{
    metric: string
    title: string
    description: string
    priority: "high" | "medium" | "low"
    effort: "low" | "medium" | "high"
  }>
}

const mockResults: WebVitalsResult = {
  url: "https://example.com",
  timestamp: new Date().toISOString(),
  metrics: [
    {
      name: "First Contentful Paint",
      value: 1.2,
      unit: "s",
      status: "good",
      description: "Time until the first text or image is painted",
      threshold: { good: 1.8, poor: 3.0 }
    },
    {
      name: "Largest Contentful Paint",
      value: 2.8,
      unit: "s", 
      status: "needs-improvement",
      description: "Time until the largest text or image is painted",
      threshold: { good: 2.5, poor: 4.0 }
    },
    {
      name: "Cumulative Layout Shift",
      value: 0.15,
      unit: "",
      status: "needs-improvement", 
      description: "Sum of all unexpected layout shift scores",
      threshold: { good: 0.1, poor: 0.25 }
    },
    {
      name: "First Input Delay",
      value: 85,
      unit: "ms",
      status: "good",
      description: "Time from first user interaction to browser response",
      threshold: { good: 100, poor: 300 }
    }
  ],
  fieldData: {
    fcp: { p75: 1.5, status: "good" },
    lcp: { p75: 3.2, status: "needs-improvement" },
    cls: { p75: 0.08, status: "good" },
    fid: { p75: 120, status: "needs-improvement" }
  },
  recommendations: [
    {
      metric: "LCP",
      title: "Optimize server response times",
      description: "Reduce server response times by optimizing your backend, using a CDN, and implementing caching strategies.",
      priority: "high",
      effort: "medium"
    },
    {
      metric: "CLS",
      title: "Set explicit dimensions for images and videos",
      description: "Always include width and height attributes on images and video elements to prevent layout shifts.",
      priority: "medium",
      effort: "low"
    },
    {
      metric: "FID",
      title: "Minimize JavaScript execution time",
      description: "Break up long tasks, optimize third-party code, and use a web worker for heavy computations.",
      priority: "medium",
      effort: "high"
    }
  ]
}

export function WebVitalsAudit() {
  const [url, setUrl] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [results, setResults] = useState<WebVitalsResult | null>(null)

  const handleAudit = async () => {
    if (!url) return
    
    setIsLoading(true)
    setTimeout(() => {
      setResults({ ...mockResults, url })
      setIsLoading(false)
    }, 2500)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "good": return "text-green-600"
      case "needs-improvement": return "text-yellow-600"
      case "poor": return "text-red-600"
      default: return "text-gray-600"
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "good": return <Badge className="bg-green-100 text-green-800">Good</Badge>
      case "needs-improvement": return <Badge variant="secondary">Needs Improvement</Badge>
      case "poor": return <Badge variant="destructive">Poor</Badge>
      default: return <Badge variant="outline">Unknown</Badge>
    }
  }

  return (
    <div className="space-y-6">
      {/* URL Input */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            Core Web Vitals Analysis
          </CardTitle>
          <CardDescription>
            Analyze your website's Core Web Vitals and get detailed performance insights
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4">
            <input
              type="url"
              placeholder="https://example.com"
              value={url}
              onChange={(e) => setUrl(e.target.value)}
              className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <Button 
              onClick={handleAudit} 
              disabled={!url || isLoading}
              className="min-w-[120px]"
            >
              {isLoading ? "Analyzing..." : "Analyze Vitals"}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Loading State */}
      {isLoading && (
        <Card>
          <CardContent className="py-8">
            <div className="text-center space-y-4">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
              <p className="text-gray-600">Measuring Core Web Vitals...</p>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Results */}
      {results && !isLoading && (
        <div className="space-y-6">
          {/* Lab Data Metrics */}
          <Card>
            <CardHeader>
              <CardTitle>Lab Data (Lighthouse)</CardTitle>
              <CardDescription>
                Controlled environment measurements from {new Date(results.timestamp).toLocaleString()}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                {results.metrics.map((metric, index) => (
                  <div key={index} className="text-center space-y-2">
                    <div className="flex items-center justify-center gap-2">
                      <Activity className="h-4 w-4 text-gray-600" />
                      <span className="text-sm font-medium">{metric.name}</span>
                    </div>
                    <div className={`text-3xl font-bold ${getStatusColor(metric.status)}`}>
                      {metric.value}{metric.unit}
                    </div>
                    {getStatusBadge(metric.status)}
                    <p className="text-xs text-gray-600">{metric.description}</p>
                    <div className="text-xs text-gray-500">
                      Good: ≤{metric.threshold.good}{metric.unit} | Poor: >{metric.threshold.poor}{metric.unit}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Field Data */}
          <Card>
            <CardHeader>
              <CardTitle>Field Data (Real User Monitoring)</CardTitle>
              <CardDescription>
                Real user experience data from Chrome User Experience Report
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div className="text-center space-y-2">
                  <div className="text-sm font-medium">First Contentful Paint</div>
                  <div className={`text-2xl font-bold ${getStatusColor(results.fieldData.fcp.status)}`}>
                    {results.fieldData.fcp.p75}s
                  </div>
                  {getStatusBadge(results.fieldData.fcp.status)}
                  <div className="text-xs text-gray-500">75th percentile</div>
                </div>
                <div className="text-center space-y-2">
                  <div className="text-sm font-medium">Largest Contentful Paint</div>
                  <div className={`text-2xl font-bold ${getStatusColor(results.fieldData.lcp.status)}`}>
                    {results.fieldData.lcp.p75}s
                  </div>
                  {getStatusBadge(results.fieldData.lcp.status)}
                  <div className="text-xs text-gray-500">75th percentile</div>
                </div>
                <div className="text-center space-y-2">
                  <div className="text-sm font-medium">Cumulative Layout Shift</div>
                  <div className={`text-2xl font-bold ${getStatusColor(results.fieldData.cls.status)}`}>
                    {results.fieldData.cls.p75}
                  </div>
                  {getStatusBadge(results.fieldData.cls.status)}
                  <div className="text-xs text-gray-500">75th percentile</div>
                </div>
                <div className="text-center space-y-2">
                  <div className="text-sm font-medium">First Input Delay</div>
                  <div className={`text-2xl font-bold ${getStatusColor(results.fieldData.fid.status)}`}>
                    {results.fieldData.fid.p75}ms
                  </div>
                  {getStatusBadge(results.fieldData.fid.status)}
                  <div className="text-xs text-gray-500">75th percentile</div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Recommendations */}
          <Card>
            <CardHeader>
              <CardTitle>Optimization Recommendations</CardTitle>
              <CardDescription>
                Prioritized suggestions to improve your Core Web Vitals
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {results.recommendations.map((rec, index) => (
                  <div key={index} className="border rounded-lg p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <Badge variant="outline" className="text-xs">
                            {rec.metric}
                          </Badge>
                          <h4 className="font-medium">{rec.title}</h4>
                        </div>
                        <p className="text-sm text-gray-600 mb-3">{rec.description}</p>
                        <div className="flex gap-2">
                          <Badge 
                            variant={rec.priority === "high" ? "destructive" : rec.priority === "medium" ? "secondary" : "outline"}
                            className="text-xs"
                          >
                            {rec.priority} priority
                          </Badge>
                          <Badge variant="outline" className="text-xs">
                            {rec.effort} effort
                          </Badge>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Historical Trend */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                Performance Trend
              </CardTitle>
              <CardDescription>
                Track your Core Web Vitals improvements over time
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-gray-500">
                <BarChart3 className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>Historical data will appear here after multiple audits</p>
                <p className="text-sm">Run regular audits to track your progress</p>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  )
}
