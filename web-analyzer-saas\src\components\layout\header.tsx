"use client";

import { useState } from "react";
import Link from "next/link";
import { cn } from "@/lib/utils";
import {
  Bell,
  Search,
  Settings,
  User,
  LogOut,
  CreditCard,
  HelpCircle,
  Menu,
  X,
  ChevronDown,
} from "@/components/ui/icons";

interface HeaderProps {
  onMenuClick?: () => void;
  className?: string;
}

export function Header({ onMenuClick, className }: HeaderProps) {
  const [isProfileOpen, setIsProfileOpen] = useState(false);
  const [isNotificationsOpen, setIsNotificationsOpen] = useState(false);

  // Mock user data - replace with actual user context
  const user = {
    name: "<PERSON>",
    email: "<EMAIL>",
    avatar: "/avatars/john-doe.jpg",
    organization: "Acme Corp",
    plan: "Pro",
  };

  // Mock notifications - replace with actual notifications
  const notifications = [
    {
      id: 1,
      title: "Audit Complete",
      message: "Website audit for example.com has finished",
      time: "2 minutes ago",
      unread: true,
    },
    {
      id: 2,
      title: "New Competitor Found",
      message: "We found a new competitor in your industry",
      time: "1 hour ago",
      unread: true,
    },
    {
      id: 3,
      title: "Monthly Report Ready",
      message: "Your monthly SEO report is ready for download",
      time: "2 hours ago",
      unread: false,
    },
  ];

  const unreadCount = notifications.filter((n) => n.unread).length;

  return (
    <header
      className={cn(
        "flex h-16 items-center justify-between border-b bg-background px-6",
        className
      )}
    >
      {/* Left side - Mobile menu button and search */}
      <div className="flex items-center gap-4">
        <button
          onClick={onMenuClick}
          className="lg:hidden rounded-md p-2 hover:bg-accent"
        >
          <Menu className="h-5 w-5" />
        </button>

        <div className="relative hidden md:block">
          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <input
            type="search"
            placeholder="Search websites, audits, reports..."
            className="w-64 rounded-md border border-input bg-background pl-10 pr-4 py-2 text-sm placeholder:text-muted-foreground focus:border-ring focus:outline-none focus:ring-1 focus:ring-ring"
          />
        </div>
      </div>

      {/* Right side - Notifications, user menu */}
      <div className="flex items-center gap-4">
        {/* Quick Actions */}
        <div className="hidden md:flex items-center gap-2">
          <Link
            href="/audit/new"
            className="rounded-md bg-primary px-4 py-2 text-sm font-medium text-primary-foreground hover:bg-primary/90"
          >
            New Audit
          </Link>
        </div>

        {/* Notifications */}
        <div className="relative">
          <button
            onClick={() => setIsNotificationsOpen(!isNotificationsOpen)}
            className="relative rounded-md p-2 hover:bg-accent"
          >
            <Bell className="h-5 w-5" />
            {unreadCount > 0 && (
              <span className="absolute -top-1 -right-1 flex h-5 w-5 items-center justify-center rounded-full bg-destructive text-xs text-destructive-foreground">
                {unreadCount}
              </span>
            )}
          </button>

          {isNotificationsOpen && (
            <div className="absolute right-0 top-full mt-2 w-80 rounded-md border bg-popover p-4 shadow-lg z-50">
              <div className="flex items-center justify-between mb-4">
                <h3 className="font-semibold">Notifications</h3>
                <button
                  onClick={() => setIsNotificationsOpen(false)}
                  className="rounded-md p-1 hover:bg-accent"
                >
                  <X className="h-4 w-4" />
                </button>
              </div>
              <div className="space-y-3">
                {notifications.map((notification) => (
                  <div
                    key={notification.id}
                    className={cn(
                      "rounded-md p-3 text-sm",
                      notification.unread ? "bg-accent" : "bg-muted/50"
                    )}
                  >
                    <div className="font-medium">{notification.title}</div>
                    <div className="text-muted-foreground">
                      {notification.message}
                    </div>
                    <div className="text-xs text-muted-foreground mt-1">
                      {notification.time}
                    </div>
                  </div>
                ))}
              </div>
              <div className="mt-4 pt-4 border-t">
                <Link
                  href="/notifications"
                  className="text-sm text-primary hover:underline"
                >
                  View all notifications
                </Link>
              </div>
            </div>
          )}
        </div>

        {/* User Menu */}
        <div className="relative">
          <button
            onClick={() => setIsProfileOpen(!isProfileOpen)}
            className="flex items-center gap-2 rounded-md p-2 hover:bg-accent"
          >
            <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary text-primary-foreground text-sm font-medium">
              {user.name
                .split(" ")
                .map((n) => n[0])
                .join("")}
            </div>
            <div className="hidden md:block text-left">
              <div className="text-sm font-medium">{user.name}</div>
              <div className="text-xs text-muted-foreground">
                {user.organization}
              </div>
            </div>
            <ChevronDown className="h-4 w-4 text-muted-foreground" />
          </button>

          {isProfileOpen && (
            <div className="absolute right-0 top-full mt-2 w-56 rounded-md border bg-popover p-2 shadow-lg z-50">
              <div className="px-3 py-2 border-b">
                <div className="font-medium">{user.name}</div>
                <div className="text-sm text-muted-foreground">
                  {user.email}
                </div>
                <div className="text-xs text-muted-foreground mt-1">
                  {user.plan} Plan • {user.organization}
                </div>
              </div>

              <div className="py-2">
                <Link
                  href="/profile"
                  className="flex items-center gap-2 rounded-md px-3 py-2 text-sm hover:bg-accent"
                >
                  <User className="h-4 w-4" />
                  Profile
                </Link>
                <Link
                  href="/organization"
                  className="flex items-center gap-2 rounded-md px-3 py-2 text-sm hover:bg-accent"
                >
                  <Settings className="h-4 w-4" />
                  Organization
                </Link>
                <Link
                  href="/subscription"
                  className="flex items-center gap-2 rounded-md px-3 py-2 text-sm hover:bg-accent"
                >
                  <CreditCard className="h-4 w-4" />
                  Billing
                </Link>
                <Link
                  href="/help"
                  className="flex items-center gap-2 rounded-md px-3 py-2 text-sm hover:bg-accent"
                >
                  <HelpCircle className="h-4 w-4" />
                  Help & Support
                </Link>
              </div>

              <div className="border-t pt-2">
                <button className="flex w-full items-center gap-2 rounded-md px-3 py-2 text-sm hover:bg-accent text-destructive">
                  <LogOut className="h-4 w-4" />
                  Sign out
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </header>
  );
}
