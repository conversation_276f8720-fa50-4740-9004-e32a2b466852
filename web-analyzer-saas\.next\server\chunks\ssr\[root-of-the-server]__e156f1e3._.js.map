{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/UI/web-analyzer-saas/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatDate(date: Date): string {\n  return new Intl.DateTimeFormat(\"en-US\", {\n    month: \"short\",\n    day: \"numeric\",\n    year: \"numeric\",\n  }).format(date)\n}\n\nexport function formatNumber(num: number): string {\n  return new Intl.NumberFormat(\"en-US\").format(num)\n}\n\nexport function formatPercentage(num: number): string {\n  return new Intl.NumberFormat(\"en-US\", {\n    style: \"percent\",\n    minimumFractionDigits: 1,\n    maximumFractionDigits: 1,\n  }).format(num / 100)\n}\n\nexport function truncateText(text: string, maxLength: number): string {\n  if (text.length <= maxLength) return text\n  return text.slice(0, maxLength) + \"...\"\n}\n\nexport function getInitials(name: string): string {\n  return name\n    .split(\" \")\n    .map((n) => n[0])\n    .join(\"\")\n    .toUpperCase()\n    .slice(0, 2)\n}\n\nexport function generateId(): string {\n  return Math.random().toString(36).substr(2, 9)\n}\n\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout)\n    timeout = setTimeout(() => func(...args), wait)\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAGO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,QAAQ,KAAK;AACtB;AAEO,SAAS,WAAW,IAAU;IACnC,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,OAAO;QACP,KAAK;QACL,MAAM;IACR,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,aAAa,GAAW;IACtC,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS,MAAM,CAAC;AAC/C;AAEO,SAAS,iBAAiB,GAAW;IAC1C,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,uBAAuB;QACvB,uBAAuB;IACzB,GAAG,MAAM,CAAC,MAAM;AAClB;AAEO,SAAS,aAAa,IAAY,EAAE,SAAiB;IAC1D,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,KAAK,CAAC,GAAG,aAAa;AACpC;AAEO,SAAS,YAAY,IAAY;IACtC,OAAO,KACJ,KAAK,CAAC,KACN,GAAG,CAAC,CAAC,IAAM,CAAC,CAAC,EAAE,EACf,IAAI,CAAC,IACL,WAAW,GACX,KAAK,CAAC,GAAG;AACd;AAEO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;AAC9C;AAEO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF", "debugId": null}}, {"offset": {"line": 104, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/UI/web-analyzer-saas/src/components/layout/sidebar.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from \"react\"\nimport Link from \"next/link\"\nimport { usePathname } from \"next/navigation\"\nimport { cn } from \"@/lib/utils\"\nimport {\n  BarChart3,\n  Search,\n  Globe,\n  Users,\n  Settings,\n  CreditCard,\n  FileText,\n  Zap,\n  Target,\n  Bot,\n  ChevronDown,\n  ChevronRight,\n  Home,\n  Activity,\n  Shield,\n  Link as LinkIcon,\n  Smartphone,\n  Code,\n  Eye,\n  TrendingUp,\n  UserCheck,\n  Database,\n  Puzzle,\n  Bell,\n  HelpCircle,\n} from \"lucide-react\"\n\ninterface NavItem {\n  title: string\n  href?: string\n  icon: React.ComponentType<{ className?: string }>\n  children?: NavItem[]\n  badge?: string\n}\n\nconst navigation: NavItem[] = [\n  {\n    title: \"Dashboard\",\n    href: \"/dashboard\",\n    icon: Home,\n  },\n  {\n    title: \"Core Audit\",\n    icon: Activity,\n    children: [\n      { title: \"PageSpeed Performance\", href: \"/audit/pagespeed\", icon: TrendingUp },\n      { title: \"Core Web Vitals\", href: \"/audit/web-vitals\", icon: Activity },\n      { title: \"Mobile Responsiveness\", href: \"/audit/mobile\", icon: Smartphone },\n      { title: \"HTTPS & SSL Check\", href: \"/audit/ssl\", icon: Shield },\n      { title: \"Broken Link Detection\", href: \"/audit/broken-links\", icon: LinkIcon },\n      { title: \"Redirect Chain Check\", href: \"/audit/redirects\", icon: Target },\n      { title: \"Minification Checks\", href: \"/audit/minification\", icon: Code },\n    ],\n  },\n  {\n    title: \"SEO Analysis\",\n    icon: Search,\n    children: [\n      { title: \"Meta Tags Audit\", href: \"/seo/meta-tags\", icon: FileText },\n      { title: \"Heading Structure\", href: \"/seo/headings\", icon: FileText },\n      { title: \"Alt Text Coverage\", href: \"/seo/alt-text\", icon: Eye },\n      { title: \"Robots.txt & Sitemap\", href: \"/seo/robots-sitemap\", icon: FileText },\n      { title: \"Internal Linking\", href: \"/seo/internal-links\", icon: LinkIcon },\n      { title: \"Structured Data\", href: \"/seo/structured-data\", icon: Code },\n    ],\n  },\n  {\n    title: \"Web Scraping\",\n    icon: Globe,\n    children: [\n      { title: \"Domain Scraping\", href: \"/scraping/domain\", icon: Globe },\n      { title: \"Targeted Scraping\", href: \"/scraping/targeted\", icon: Target },\n      { title: \"Price & Review Extraction\", href: \"/scraping/price-review\", icon: BarChart3 },\n      { title: \"HTML & Metadata\", href: \"/scraping/html-metadata\", icon: Code },\n      { title: \"Scraper Rule Builder\", href: \"/scraping/rule-builder\", icon: Puzzle },\n    ],\n  },\n  {\n    title: \"Competitor Analysis\",\n    icon: Users,\n    children: [\n      { title: \"Keyword Gap Analysis\", href: \"/competitor/keyword-gap\", icon: Search },\n      { title: \"Side-by-side SEO\", href: \"/competitor/seo-comparison\", icon: BarChart3 },\n      { title: \"Backlink Analysis\", href: \"/competitor/backlinks\", icon: LinkIcon },\n      { title: \"Traffic Estimation\", href: \"/competitor/traffic\", icon: TrendingUp },\n      { title: \"Audit Benchmarking\", href: \"/competitor/benchmarking\", icon: Target },\n    ],\n  },\n  {\n    title: \"AI Features\",\n    icon: Bot,\n    children: [\n      { title: \"Audit Summary\", href: \"/ai/audit-summary\", icon: FileText },\n      { title: \"Fix Prioritization\", href: \"/ai/fix-priority\", icon: Target },\n      { title: \"Keyword Insights\", href: \"/ai/keyword-insights\", icon: Search },\n      { title: \"Content Analysis\", href: \"/ai/content-analysis\", icon: FileText },\n      { title: \"Ask AI Anything\", href: \"/ai/chat\", icon: Bot },\n      { title: \"Blog Idea Generator\", href: \"/ai/blog-ideas\", icon: Zap },\n      { title: \"Meta Tag Generator\", href: \"/ai/meta-generator\", icon: Code },\n    ],\n  },\n  {\n    title: \"Reports\",\n    icon: FileText,\n    children: [\n      { title: \"PDF Reports\", href: \"/reports/pdf\", icon: FileText },\n      { title: \"Email Reports\", href: \"/reports/email\", icon: Bell },\n      { title: \"Shareable Links\", href: \"/reports/shareable\", icon: LinkIcon },\n      { title: \"Scheduled Reports\", href: \"/reports/scheduled\", icon: Bell },\n    ],\n  },\n  {\n    title: \"Organization\",\n    href: \"/organization\",\n    icon: Users,\n  },\n  {\n    title: \"Subscription\",\n    href: \"/subscription\",\n    icon: CreditCard,\n  },\n  {\n    title: \"Integrations\",\n    href: \"/integrations\",\n    icon: Puzzle,\n  },\n  {\n    title: \"Settings\",\n    href: \"/settings\",\n    icon: Settings,\n  },\n]\n\ninterface SidebarProps {\n  className?: string\n}\n\nexport function Sidebar({ className }: SidebarProps) {\n  const pathname = usePathname()\n  const [expandedItems, setExpandedItems] = useState<string[]>([])\n\n  const toggleExpanded = (title: string) => {\n    setExpandedItems(prev =>\n      prev.includes(title)\n        ? prev.filter(item => item !== title)\n        : [...prev, title]\n    )\n  }\n\n  const renderNavItem = (item: NavItem, level = 0) => {\n    const isExpanded = expandedItems.includes(item.title)\n    const hasChildren = item.children && item.children.length > 0\n    const isActive = item.href ? pathname === item.href : false\n\n    return (\n      <div key={item.title}>\n        {item.href ? (\n          <Link\n            href={item.href}\n            className={cn(\n              \"flex items-center gap-3 rounded-lg px-3 py-2 text-sm font-medium transition-colors\",\n              \"hover:bg-accent hover:text-accent-foreground\",\n              isActive && \"bg-accent text-accent-foreground\",\n              level > 0 && \"ml-6\"\n            )}\n          >\n            <item.icon className=\"h-4 w-4\" />\n            {item.title}\n            {item.badge && (\n              <span className=\"ml-auto rounded-full bg-primary px-2 py-1 text-xs text-primary-foreground\">\n                {item.badge}\n              </span>\n            )}\n          </Link>\n        ) : (\n          <button\n            onClick={() => hasChildren && toggleExpanded(item.title)}\n            className={cn(\n              \"flex w-full items-center gap-3 rounded-lg px-3 py-2 text-sm font-medium transition-colors\",\n              \"hover:bg-accent hover:text-accent-foreground\",\n              level > 0 && \"ml-6\"\n            )}\n          >\n            <item.icon className=\"h-4 w-4\" />\n            {item.title}\n            {item.badge && (\n              <span className=\"ml-auto rounded-full bg-primary px-2 py-1 text-xs text-primary-foreground\">\n                {item.badge}\n              </span>\n            )}\n            {hasChildren && (\n              <div className=\"ml-auto\">\n                {isExpanded ? (\n                  <ChevronDown className=\"h-4 w-4\" />\n                ) : (\n                  <ChevronRight className=\"h-4 w-4\" />\n                )}\n              </div>\n            )}\n          </button>\n        )}\n        {hasChildren && isExpanded && (\n          <div className=\"mt-1 space-y-1\">\n            {item.children?.map(child => renderNavItem(child, level + 1))}\n          </div>\n        )}\n      </div>\n    )\n  }\n\n  return (\n    <div className={cn(\"flex h-full w-64 flex-col border-r bg-card\", className)}>\n      <div className=\"flex h-16 items-center border-b px-6\">\n        <Link href=\"/dashboard\" className=\"flex items-center gap-2 font-semibold\">\n          <Bot className=\"h-6 w-6 text-primary\" />\n          <span>WebAnalyzer Pro</span>\n        </Link>\n      </div>\n      <nav className=\"flex-1 space-y-1 p-4 overflow-y-auto\">\n        {navigation.map(item => renderNavItem(item))}\n      </nav>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;;;;AALA;;;;;;;AA0CA,MAAM,aAAwB;IAC5B;QACE,OAAO;QACP,MAAM;QACN,MAAM;IACR;IACA;QACE,OAAO;QACP,MAAM;QACN,UAAU;YACR;gBAAE,OAAO;gBAAyB,MAAM;gBAAoB,MAAM;YAAW;YAC7E;gBAAE,OAAO;gBAAmB,MAAM;gBAAqB,MAAM;YAAS;YACtE;gBAAE,OAAO;gBAAyB,MAAM;gBAAiB,MAAM;YAAW;YAC1E;gBAAE,OAAO;gBAAqB,MAAM;gBAAc,MAAM;YAAO;YAC/D;gBAAE,OAAO;gBAAyB,MAAM;gBAAuB,MAAM;YAAS;YAC9E;gBAAE,OAAO;gBAAwB,MAAM;gBAAoB,MAAM;YAAO;YACxE;gBAAE,OAAO;gBAAuB,MAAM;gBAAuB,MAAM;YAAK;SACzE;IACH;IACA;QACE,OAAO;QACP,MAAM;QACN,UAAU;YACR;gBAAE,OAAO;gBAAmB,MAAM;gBAAkB,MAAM;YAAS;YACnE;gBAAE,OAAO;gBAAqB,MAAM;gBAAiB,MAAM;YAAS;YACpE;gBAAE,OAAO;gBAAqB,MAAM;gBAAiB,MAAM;YAAI;YAC/D;gBAAE,OAAO;gBAAwB,MAAM;gBAAuB,MAAM;YAAS;YAC7E;gBAAE,OAAO;gBAAoB,MAAM;gBAAuB,MAAM;YAAS;YACzE;gBAAE,OAAO;gBAAmB,MAAM;gBAAwB,MAAM;YAAK;SACtE;IACH;IACA;QACE,OAAO;QACP,MAAM;QACN,UAAU;YACR;gBAAE,OAAO;gBAAmB,MAAM;gBAAoB,MAAM;YAAM;YAClE;gBAAE,OAAO;gBAAqB,MAAM;gBAAsB,MAAM;YAAO;YACvE;gBAAE,OAAO;gBAA6B,MAAM;gBAA0B,MAAM;YAAU;YACtF;gBAAE,OAAO;gBAAmB,MAAM;gBAA2B,MAAM;YAAK;YACxE;gBAAE,OAAO;gBAAwB,MAAM;gBAA0B,MAAM;YAAO;SAC/E;IACH;IACA;QACE,OAAO;QACP,MAAM;QACN,UAAU;YACR;gBAAE,OAAO;gBAAwB,MAAM;gBAA2B,MAAM;YAAO;YAC/E;gBAAE,OAAO;gBAAoB,MAAM;gBAA8B,MAAM;YAAU;YACjF;gBAAE,OAAO;gBAAqB,MAAM;gBAAyB,MAAM;YAAS;YAC5E;gBAAE,OAAO;gBAAsB,MAAM;gBAAuB,MAAM;YAAW;YAC7E;gBAAE,OAAO;gBAAsB,MAAM;gBAA4B,MAAM;YAAO;SAC/E;IACH;IACA;QACE,OAAO;QACP,MAAM;QACN,UAAU;YACR;gBAAE,OAAO;gBAAiB,MAAM;gBAAqB,MAAM;YAAS;YACpE;gBAAE,OAAO;gBAAsB,MAAM;gBAAoB,MAAM;YAAO;YACtE;gBAAE,OAAO;gBAAoB,MAAM;gBAAwB,MAAM;YAAO;YACxE;gBAAE,OAAO;gBAAoB,MAAM;gBAAwB,MAAM;YAAS;YAC1E;gBAAE,OAAO;gBAAmB,MAAM;gBAAY,MAAM;YAAI;YACxD;gBAAE,OAAO;gBAAuB,MAAM;gBAAkB,MAAM;YAAI;YAClE;gBAAE,OAAO;gBAAsB,MAAM;gBAAsB,MAAM;YAAK;SACvE;IACH;IACA;QACE,OAAO;QACP,MAAM;QACN,UAAU;YACR;gBAAE,OAAO;gBAAe,MAAM;gBAAgB,MAAM;YAAS;YAC7D;gBAAE,OAAO;gBAAiB,MAAM;gBAAkB,MAAM;YAAK;YAC7D;gBAAE,OAAO;gBAAmB,MAAM;gBAAsB,MAAM;YAAS;YACvE;gBAAE,OAAO;gBAAqB,MAAM;gBAAsB,MAAM;YAAK;SACtE;IACH;IACA;QACE,OAAO;QACP,MAAM;QACN,MAAM;IACR;IACA;QACE,OAAO;QACP,MAAM;QACN,MAAM;IACR;IACA;QACE,OAAO;QACP,MAAM;QACN,MAAM;IACR;IACA;QACE,OAAO;QACP,MAAM;QACN,MAAM;IACR;CACD;AAMM,SAAS,QAAQ,EAAE,SAAS,EAAgB;IACjD,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAE/D,MAAM,iBAAiB,CAAC;QACtB,iBAAiB,CAAA,OACf,KAAK,QAAQ,CAAC,SACV,KAAK,MAAM,CAAC,CAAA,OAAQ,SAAS,SAC7B;mBAAI;gBAAM;aAAM;IAExB;IAEA,MAAM,gBAAgB,CAAC,MAAe,QAAQ,CAAC;QAC7C,MAAM,aAAa,cAAc,QAAQ,CAAC,KAAK,KAAK;QACpD,MAAM,cAAc,KAAK,QAAQ,IAAI,KAAK,QAAQ,CAAC,MAAM,GAAG;QAC5D,MAAM,WAAW,KAAK,IAAI,GAAG,aAAa,KAAK,IAAI,GAAG;QAEtD,qBACE,8OAAC;;gBACE,KAAK,IAAI,iBACR,8OAAC,4JAAA,CAAA,UAAI;oBACH,MAAM,KAAK,IAAI;oBACf,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sFACA,gDACA,YAAY,oCACZ,QAAQ,KAAK;;sCAGf,8OAAC,KAAK,IAAI;4BAAC,WAAU;;;;;;wBACpB,KAAK,KAAK;wBACV,KAAK,KAAK,kBACT,8OAAC;4BAAK,WAAU;sCACb,KAAK,KAAK;;;;;;;;;;;yCAKjB,8OAAC;oBACC,SAAS,IAAM,eAAe,eAAe,KAAK,KAAK;oBACvD,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6FACA,gDACA,QAAQ,KAAK;;sCAGf,8OAAC,KAAK,IAAI;4BAAC,WAAU;;;;;;wBACpB,KAAK,KAAK;wBACV,KAAK,KAAK,kBACT,8OAAC;4BAAK,WAAU;sCACb,KAAK,KAAK;;;;;;wBAGd,6BACC,8OAAC;4BAAI,WAAU;sCACZ,2BACC,8OAAC;gCAAY,WAAU;;;;;qDAEvB,8OAAC;gCAAa,WAAU;;;;;;;;;;;;;;;;;gBAMjC,eAAe,4BACd,8OAAC;oBAAI,WAAU;8BACZ,KAAK,QAAQ,EAAE,IAAI,CAAA,QAAS,cAAc,OAAO,QAAQ;;;;;;;WAhDtD,KAAK,KAAK;;;;;IAqDxB;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8CAA8C;;0BAC/D,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;oBAAC,MAAK;oBAAa,WAAU;;sCAChC,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;sCAAK;;;;;;;;;;;;;;;;;0BAGV,8OAAC;gBAAI,WAAU;0BACZ,WAAW,GAAG,CAAC,CAAA,OAAQ,cAAc;;;;;;;;;;;;AAI9C", "debugId": null}}, {"offset": {"line": 513, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/UI/web-analyzer-saas/src/components/layout/header.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from \"react\"\nimport Link from \"next/link\"\nimport { cn } from \"@/lib/utils\"\nimport {\n  Bell,\n  Search,\n  Settings,\n  User,\n  LogOut,\n  CreditCard,\n  HelpCircle,\n  Menu,\n  X,\n  ChevronDown,\n} from \"lucide-react\"\n\ninterface HeaderProps {\n  onMenuClick?: () => void\n  className?: string\n}\n\nexport function Header({ onMenuClick, className }: HeaderProps) {\n  const [isProfileOpen, setIsProfileOpen] = useState(false)\n  const [isNotificationsOpen, setIsNotificationsOpen] = useState(false)\n\n  // Mock user data - replace with actual user context\n  const user = {\n    name: \"<PERSON>\",\n    email: \"<EMAIL>\",\n    avatar: \"/avatars/john-doe.jpg\",\n    organization: \"Acme Corp\",\n    plan: \"Pro\",\n  }\n\n  // Mock notifications - replace with actual notifications\n  const notifications = [\n    {\n      id: 1,\n      title: \"Audit Complete\",\n      message: \"Website audit for example.com has finished\",\n      time: \"2 minutes ago\",\n      unread: true,\n    },\n    {\n      id: 2,\n      title: \"New Competitor Found\",\n      message: \"We found a new competitor in your industry\",\n      time: \"1 hour ago\",\n      unread: true,\n    },\n    {\n      id: 3,\n      title: \"Monthly Report Ready\",\n      message: \"Your monthly SEO report is ready for download\",\n      time: \"2 hours ago\",\n      unread: false,\n    },\n  ]\n\n  const unreadCount = notifications.filter(n => n.unread).length\n\n  return (\n    <header className={cn(\"flex h-16 items-center justify-between border-b bg-background px-6\", className)}>\n      {/* Left side - Mobile menu button and search */}\n      <div className=\"flex items-center gap-4\">\n        <button\n          onClick={onMenuClick}\n          className=\"lg:hidden rounded-md p-2 hover:bg-accent\"\n        >\n          <Menu className=\"h-5 w-5\" />\n        </button>\n        \n        <div className=\"relative hidden md:block\">\n          <Search className=\"absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground\" />\n          <input\n            type=\"search\"\n            placeholder=\"Search websites, audits, reports...\"\n            className=\"w-64 rounded-md border border-input bg-background pl-10 pr-4 py-2 text-sm placeholder:text-muted-foreground focus:border-ring focus:outline-none focus:ring-1 focus:ring-ring\"\n          />\n        </div>\n      </div>\n\n      {/* Right side - Notifications, user menu */}\n      <div className=\"flex items-center gap-4\">\n        {/* Quick Actions */}\n        <div className=\"hidden md:flex items-center gap-2\">\n          <Link\n            href=\"/audit/new\"\n            className=\"rounded-md bg-primary px-4 py-2 text-sm font-medium text-primary-foreground hover:bg-primary/90\"\n          >\n            New Audit\n          </Link>\n        </div>\n\n        {/* Notifications */}\n        <div className=\"relative\">\n          <button\n            onClick={() => setIsNotificationsOpen(!isNotificationsOpen)}\n            className=\"relative rounded-md p-2 hover:bg-accent\"\n          >\n            <Bell className=\"h-5 w-5\" />\n            {unreadCount > 0 && (\n              <span className=\"absolute -top-1 -right-1 flex h-5 w-5 items-center justify-center rounded-full bg-destructive text-xs text-destructive-foreground\">\n                {unreadCount}\n              </span>\n            )}\n          </button>\n\n          {isNotificationsOpen && (\n            <div className=\"absolute right-0 top-full mt-2 w-80 rounded-md border bg-popover p-4 shadow-lg z-50\">\n              <div className=\"flex items-center justify-between mb-4\">\n                <h3 className=\"font-semibold\">Notifications</h3>\n                <button\n                  onClick={() => setIsNotificationsOpen(false)}\n                  className=\"rounded-md p-1 hover:bg-accent\"\n                >\n                  <X className=\"h-4 w-4\" />\n                </button>\n              </div>\n              <div className=\"space-y-3\">\n                {notifications.map((notification) => (\n                  <div\n                    key={notification.id}\n                    className={cn(\n                      \"rounded-md p-3 text-sm\",\n                      notification.unread ? \"bg-accent\" : \"bg-muted/50\"\n                    )}\n                  >\n                    <div className=\"font-medium\">{notification.title}</div>\n                    <div className=\"text-muted-foreground\">{notification.message}</div>\n                    <div className=\"text-xs text-muted-foreground mt-1\">{notification.time}</div>\n                  </div>\n                ))}\n              </div>\n              <div className=\"mt-4 pt-4 border-t\">\n                <Link\n                  href=\"/notifications\"\n                  className=\"text-sm text-primary hover:underline\"\n                >\n                  View all notifications\n                </Link>\n              </div>\n            </div>\n          )}\n        </div>\n\n        {/* User Menu */}\n        <div className=\"relative\">\n          <button\n            onClick={() => setIsProfileOpen(!isProfileOpen)}\n            className=\"flex items-center gap-2 rounded-md p-2 hover:bg-accent\"\n          >\n            <div className=\"flex h-8 w-8 items-center justify-center rounded-full bg-primary text-primary-foreground text-sm font-medium\">\n              {user.name.split(' ').map(n => n[0]).join('')}\n            </div>\n            <div className=\"hidden md:block text-left\">\n              <div className=\"text-sm font-medium\">{user.name}</div>\n              <div className=\"text-xs text-muted-foreground\">{user.organization}</div>\n            </div>\n            <ChevronDown className=\"h-4 w-4 text-muted-foreground\" />\n          </button>\n\n          {isProfileOpen && (\n            <div className=\"absolute right-0 top-full mt-2 w-56 rounded-md border bg-popover p-2 shadow-lg z-50\">\n              <div className=\"px-3 py-2 border-b\">\n                <div className=\"font-medium\">{user.name}</div>\n                <div className=\"text-sm text-muted-foreground\">{user.email}</div>\n                <div className=\"text-xs text-muted-foreground mt-1\">\n                  {user.plan} Plan • {user.organization}\n                </div>\n              </div>\n              \n              <div className=\"py-2\">\n                <Link\n                  href=\"/profile\"\n                  className=\"flex items-center gap-2 rounded-md px-3 py-2 text-sm hover:bg-accent\"\n                >\n                  <User className=\"h-4 w-4\" />\n                  Profile\n                </Link>\n                <Link\n                  href=\"/organization\"\n                  className=\"flex items-center gap-2 rounded-md px-3 py-2 text-sm hover:bg-accent\"\n                >\n                  <Settings className=\"h-4 w-4\" />\n                  Organization\n                </Link>\n                <Link\n                  href=\"/subscription\"\n                  className=\"flex items-center gap-2 rounded-md px-3 py-2 text-sm hover:bg-accent\"\n                >\n                  <CreditCard className=\"h-4 w-4\" />\n                  Billing\n                </Link>\n                <Link\n                  href=\"/help\"\n                  className=\"flex items-center gap-2 rounded-md px-3 py-2 text-sm hover:bg-accent\"\n                >\n                  <HelpCircle className=\"h-4 w-4\" />\n                  Help & Support\n                </Link>\n              </div>\n              \n              <div className=\"border-t pt-2\">\n                <button className=\"flex w-full items-center gap-2 rounded-md px-3 py-2 text-sm hover:bg-accent text-destructive\">\n                  <LogOut className=\"h-4 w-4\" />\n                  Sign out\n                </button>\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n    </header>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;;;;AAJA;;;;;;AAuBO,SAAS,OAAO,EAAE,WAAW,EAAE,SAAS,EAAe;IAC5D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/D,oDAAoD;IACpD,MAAM,OAAO;QACX,MAAM;QACN,OAAO;QACP,QAAQ;QACR,cAAc;QACd,MAAM;IACR;IAEA,yDAAyD;IACzD,MAAM,gBAAgB;QACpB;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,MAAM;YACN,QAAQ;QACV;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,MAAM;YACN,QAAQ;QACV;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,MAAM;YACN,QAAQ;QACV;KACD;IAED,MAAM,cAAc,cAAc,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,EAAE,MAAM;IAE9D,qBACE,8OAAC;QAAO,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sEAAsE;;0BAE1F,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,SAAS;wBACT,WAAU;kCAEV,cAAA,8OAAC;4BAAK,WAAU;;;;;;;;;;;kCAGlB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAO,WAAU;;;;;;0CAClB,8OAAC;gCACC,MAAK;gCACL,aAAY;gCACZ,WAAU;;;;;;;;;;;;;;;;;;0BAMhB,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;sCACX;;;;;;;;;;;kCAMH,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,SAAS,IAAM,uBAAuB,CAAC;gCACvC,WAAU;;kDAEV,8OAAC;wCAAK,WAAU;;;;;;oCACf,cAAc,mBACb,8OAAC;wCAAK,WAAU;kDACb;;;;;;;;;;;;4BAKN,qCACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAgB;;;;;;0DAC9B,8OAAC;gDACC,SAAS,IAAM,uBAAuB;gDACtC,WAAU;0DAEV,cAAA,8OAAC;oDAAE,WAAU;;;;;;;;;;;;;;;;;kDAGjB,8OAAC;wCAAI,WAAU;kDACZ,cAAc,GAAG,CAAC,CAAC,6BAClB,8OAAC;gDAEC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0BACA,aAAa,MAAM,GAAG,cAAc;;kEAGtC,8OAAC;wDAAI,WAAU;kEAAe,aAAa,KAAK;;;;;;kEAChD,8OAAC;wDAAI,WAAU;kEAAyB,aAAa,OAAO;;;;;;kEAC5D,8OAAC;wDAAI,WAAU;kEAAsC,aAAa,IAAI;;;;;;;+CARjE,aAAa,EAAE;;;;;;;;;;kDAY1B,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;kCAST,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,SAAS,IAAM,iBAAiB,CAAC;gCACjC,WAAU;;kDAEV,8OAAC;wCAAI,WAAU;kDACZ,KAAK,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,IAAK,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC;;;;;;kDAE5C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAAuB,KAAK,IAAI;;;;;;0DAC/C,8OAAC;gDAAI,WAAU;0DAAiC,KAAK,YAAY;;;;;;;;;;;;kDAEnE,8OAAC;wCAAY,WAAU;;;;;;;;;;;;4BAGxB,+BACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAAe,KAAK,IAAI;;;;;;0DACvC,8OAAC;gDAAI,WAAU;0DAAiC,KAAK,KAAK;;;;;;0DAC1D,8OAAC;gDAAI,WAAU;;oDACZ,KAAK,IAAI;oDAAC;oDAAS,KAAK,YAAY;;;;;;;;;;;;;kDAIzC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;;kEAEV,8OAAC;wDAAK,WAAU;;;;;;oDAAY;;;;;;;0DAG9B,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;;kEAEV,8OAAC;wDAAS,WAAU;;;;;;oDAAY;;;;;;;0DAGlC,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;;kEAEV,8OAAC;wDAAW,WAAU;;;;;;oDAAY;;;;;;;0DAGpC,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;;kEAEV,8OAAC;wDAAW,WAAU;;;;;;oDAAY;;;;;;;;;;;;;kDAKtC,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAO,WAAU;;8DAChB,8OAAC;oDAAO,WAAU;;;;;;gDAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUhD", "debugId": null}}, {"offset": {"line": 998, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/UI/web-analyzer-saas/src/components/layout/app-layout.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from \"react\"\nimport { cn } from \"@/lib/utils\"\nimport { Sidebar } from \"./sidebar\"\nimport { Header } from \"./header\"\n\ninterface AppLayoutProps {\n  children: React.ReactNode\n  className?: string\n}\n\nexport function AppLayout({ children, className }: AppLayoutProps) {\n  const [sidebarOpen, setSidebarOpen] = useState(false)\n\n  return (\n    <div className=\"flex h-screen bg-background\">\n      {/* Desktop Sidebar */}\n      <div className=\"hidden lg:block\">\n        <Sidebar />\n      </div>\n\n      {/* Mobile Sidebar Overlay */}\n      {sidebarOpen && (\n        <div className=\"fixed inset-0 z-50 lg:hidden\">\n          <div \n            className=\"absolute inset-0 bg-black/50\" \n            onClick={() => setSidebarOpen(false)}\n          />\n          <div className=\"absolute left-0 top-0 h-full\">\n            <Sidebar />\n          </div>\n        </div>\n      )}\n\n      {/* Main Content */}\n      <div className=\"flex flex-1 flex-col overflow-hidden\">\n        <Header onMenuClick={() => setSidebarOpen(true)} />\n        <main className={cn(\"flex-1 overflow-y-auto p-6\", className)}>\n          {children}\n        </main>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAYO,SAAS,UAAU,EAAE,QAAQ,EAAE,SAAS,EAAkB;IAC/D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,uIAAA,CAAA,UAAO;;;;;;;;;;YAIT,6BACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,WAAU;wBACV,SAAS,IAAM,eAAe;;;;;;kCAEhC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,uIAAA,CAAA,UAAO;;;;;;;;;;;;;;;;0BAMd,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,sIAAA,CAAA,SAAM;wBAAC,aAAa,IAAM,eAAe;;;;;;kCAC1C,8OAAC;wBAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;kCAC/C;;;;;;;;;;;;;;;;;;AAKX", "debugId": null}}, {"offset": {"line": 1095, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/UI/web-analyzer-saas/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1176, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/UI/web-analyzer-saas/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    return (\n      <button\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;;;;;;AAEA;;;;;AAEA,MAAM,iBAAiB,IACrB,0RACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1237, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/UI/web-analyzer-saas/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        destructive:\n          \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n        outline: \"text-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;;AAEA,MAAM,gBAAgB,IACpB,0KACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;QACX;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE", "debugId": null}}, {"offset": {"line": 1283, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/UI/web-analyzer-saas/src/components/ui/progress.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\ninterface ProgressProps extends React.HTMLAttributes<HTMLDivElement> {\n  value?: number\n  max?: number\n}\n\nconst Progress = React.forwardRef<HTMLDivElement, ProgressProps>(\n  ({ className, value = 0, max = 100, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn(\n        \"relative h-4 w-full overflow-hidden rounded-full bg-secondary\",\n        className\n      )}\n      {...props}\n    >\n      <div\n        className=\"h-full w-full flex-1 bg-primary transition-all\"\n        style={{ transform: `translateX(-${100 - (value / max) * 100}%)` }}\n      />\n    </div>\n  )\n)\nProgress.displayName = \"Progress\"\n\nexport { Progress }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAOA,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC9B,CAAC,EAAE,SAAS,EAAE,QAAQ,CAAC,EAAE,MAAM,GAAG,EAAE,GAAG,OAAO,EAAE,oBAC9C,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC;YACC,WAAU;YACV,OAAO;gBAAE,WAAW,CAAC,YAAY,EAAE,MAAM,AAAC,QAAQ,MAAO,IAAI,EAAE,CAAC;YAAC;;;;;;;;;;;AAKzE,SAAS,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1319, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/UI/web-analyzer-saas/src/components/dashboard/dashboard-overview.tsx"], "sourcesContent": ["\"use client\"\n\nimport { <PERSON>, <PERSON><PERSON>ontent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from \"@/components/ui/card\"\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { Progress } from \"@/components/ui/progress\"\nimport {\n  BarChart3,\n  Globe,\n  TrendingUp,\n  TrendingDown,\n  Activity,\n  Users,\n  Clock,\n  CheckCircle,\n  AlertTriangle,\n  XCircle,\n  Plus,\n  ArrowRight,\n  Zap,\n} from \"lucide-react\"\nimport Link from \"next/link\"\n\n// Mock data - replace with real data from your API\nconst stats = [\n  {\n    title: \"Total Websites\",\n    value: \"24\",\n    change: \"+2 this month\",\n    trend: \"up\",\n    icon: Globe,\n  },\n  {\n    title: \"Audits Completed\",\n    value: \"156\",\n    change: \"+12 this week\",\n    trend: \"up\",\n    icon: CheckCircle,\n  },\n  {\n    title: \"Issues Found\",\n    value: \"89\",\n    change: \"-5 from last audit\",\n    trend: \"down\",\n    icon: AlertTriangle,\n  },\n  {\n    title: \"AI Credits Used\",\n    value: \"2,340\",\n    change: \"760 remaining\",\n    trend: \"neutral\",\n    icon: Zap,\n  },\n]\n\nconst recentAudits = [\n  {\n    id: 1,\n    website: \"example.com\",\n    type: \"Full Audit\",\n    status: \"completed\",\n    score: 85,\n    issues: 12,\n    completedAt: \"2 hours ago\",\n  },\n  {\n    id: 2,\n    website: \"mystore.com\",\n    type: \"SEO Audit\",\n    status: \"in-progress\",\n    score: null,\n    issues: null,\n    completedAt: null,\n  },\n  {\n    id: 3,\n    website: \"blog.example.com\",\n    type: \"Performance Audit\",\n    status: \"completed\",\n    score: 92,\n    issues: 3,\n    completedAt: \"1 day ago\",\n  },\n  {\n    id: 4,\n    website: \"shop.example.com\",\n    type: \"Full Audit\",\n    status: \"failed\",\n    score: null,\n    issues: null,\n    completedAt: \"2 days ago\",\n  },\n]\n\nconst quickActions = [\n  {\n    title: \"New Website Audit\",\n    description: \"Start a comprehensive audit of a new website\",\n    href: \"/audit/new\",\n    icon: Activity,\n    color: \"bg-blue-500\",\n  },\n  {\n    title: \"Competitor Analysis\",\n    description: \"Compare your site against competitors\",\n    href: \"/competitor/new\",\n    icon: Users,\n    color: \"bg-green-500\",\n  },\n  {\n    title: \"AI Content Analysis\",\n    description: \"Get AI-powered insights on your content\",\n    href: \"/ai/content-analysis\",\n    icon: Zap,\n    color: \"bg-purple-500\",\n  },\n  {\n    title: \"Generate Report\",\n    description: \"Create a white-label PDF report\",\n    href: \"/reports/new\",\n    icon: BarChart3,\n    color: \"bg-orange-500\",\n  },\n]\n\nexport function DashboardOverview() {\n  return (\n    <div className=\"space-y-6\">\n      {/* Stats Grid */}\n      <div className=\"grid gap-4 md:grid-cols-2 lg:grid-cols-4\">\n        {stats.map((stat) => (\n          <Card key={stat.title}>\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n              <CardTitle className=\"text-sm font-medium\">{stat.title}</CardTitle>\n              <stat.icon className=\"h-4 w-4 text-muted-foreground\" />\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold\">{stat.value}</div>\n              <p className={`text-xs ${\n                stat.trend === \"up\" ? \"text-green-600\" : \n                stat.trend === \"down\" ? \"text-red-600\" : \n                \"text-muted-foreground\"\n              }`}>\n                {stat.trend === \"up\" && <TrendingUp className=\"inline h-3 w-3 mr-1\" />}\n                {stat.trend === \"down\" && <TrendingDown className=\"inline h-3 w-3 mr-1\" />}\n                {stat.change}\n              </p>\n            </CardContent>\n          </Card>\n        ))}\n      </div>\n\n      <div className=\"grid gap-6 lg:grid-cols-3\">\n        {/* Recent Audits */}\n        <Card className=\"lg:col-span-2\">\n          <CardHeader>\n            <CardTitle>Recent Audits</CardTitle>\n            <CardDescription>\n              Your latest website audits and their results\n            </CardDescription>\n          </CardHeader>\n          <CardContent>\n            <div className=\"space-y-4\">\n              {recentAudits.map((audit) => (\n                <div key={audit.id} className=\"flex items-center justify-between p-4 border rounded-lg\">\n                  <div className=\"flex items-center space-x-4\">\n                    <div className=\"flex-1\">\n                      <div className=\"flex items-center gap-2\">\n                        <h4 className=\"font-medium\">{audit.website}</h4>\n                        <Badge variant=\"outline\">{audit.type}</Badge>\n                      </div>\n                      <div className=\"flex items-center gap-4 mt-1 text-sm text-muted-foreground\">\n                        <span className=\"flex items-center gap-1\">\n                          {audit.status === \"completed\" && <CheckCircle className=\"h-3 w-3 text-green-500\" />}\n                          {audit.status === \"in-progress\" && <Clock className=\"h-3 w-3 text-yellow-500\" />}\n                          {audit.status === \"failed\" && <XCircle className=\"h-3 w-3 text-red-500\" />}\n                          {audit.status}\n                        </span>\n                        {audit.completedAt && <span>{audit.completedAt}</span>}\n                      </div>\n                    </div>\n                  </div>\n                  <div className=\"flex items-center gap-4\">\n                    {audit.score && (\n                      <div className=\"text-right\">\n                        <div className=\"text-lg font-semibold\">{audit.score}</div>\n                        <div className=\"text-xs text-muted-foreground\">Score</div>\n                      </div>\n                    )}\n                    {audit.issues !== null && (\n                      <div className=\"text-right\">\n                        <div className=\"text-lg font-semibold text-orange-600\">{audit.issues}</div>\n                        <div className=\"text-xs text-muted-foreground\">Issues</div>\n                      </div>\n                    )}\n                    <Button variant=\"ghost\" size=\"sm\">\n                      <ArrowRight className=\"h-4 w-4\" />\n                    </Button>\n                  </div>\n                </div>\n              ))}\n            </div>\n            <div className=\"mt-4\">\n              <Button variant=\"outline\" className=\"w-full\">\n                View All Audits\n              </Button>\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* Quick Actions */}\n        <Card>\n          <CardHeader>\n            <CardTitle>Quick Actions</CardTitle>\n            <CardDescription>\n              Start your next analysis\n            </CardDescription>\n          </CardHeader>\n          <CardContent>\n            <div className=\"space-y-3\">\n              {quickActions.map((action) => (\n                <Link key={action.title} href={action.href}>\n                  <div className=\"flex items-center gap-3 p-3 rounded-lg border hover:bg-accent transition-colors cursor-pointer\">\n                    <div className={`p-2 rounded-md ${action.color}`}>\n                      <action.icon className=\"h-4 w-4 text-white\" />\n                    </div>\n                    <div className=\"flex-1\">\n                      <div className=\"font-medium text-sm\">{action.title}</div>\n                      <div className=\"text-xs text-muted-foreground\">{action.description}</div>\n                    </div>\n                  </div>\n                </Link>\n              ))}\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n\n      {/* Usage Overview */}\n      <Card>\n        <CardHeader>\n          <CardTitle>Monthly Usage</CardTitle>\n          <CardDescription>\n            Your current plan usage and limits\n          </CardDescription>\n        </CardHeader>\n        <CardContent>\n          <div className=\"grid gap-6 md:grid-cols-3\">\n            <div className=\"space-y-2\">\n              <div className=\"flex justify-between text-sm\">\n                <span>Website Audits</span>\n                <span>24 / 50</span>\n              </div>\n              <Progress value={48} className=\"h-2\" />\n            </div>\n            <div className=\"space-y-2\">\n              <div className=\"flex justify-between text-sm\">\n                <span>AI Credits</span>\n                <span>2,340 / 5,000</span>\n              </div>\n              <Progress value={47} className=\"h-2\" />\n            </div>\n            <div className=\"space-y-2\">\n              <div className=\"flex justify-between text-sm\">\n                <span>Team Members</span>\n                <span>3 / 10</span>\n              </div>\n              <Progress value={30} className=\"h-2\" />\n            </div>\n          </div>\n          <div className=\"mt-4 flex justify-between items-center\">\n            <p className=\"text-sm text-muted-foreground\">\n              Pro Plan • Resets in 12 days\n            </p>\n            <Button variant=\"outline\" size=\"sm\">\n              Upgrade Plan\n            </Button>\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;;;;AAgBA;AArBA;;;;;;;;AAuBA,mDAAmD;AACnD,MAAM,QAAQ;IACZ;QACE,OAAO;QACP,OAAO;QACP,QAAQ;QACR,OAAO;QACP,MAAM;IACR;IACA;QACE,OAAO;QACP,OAAO;QACP,QAAQ;QACR,OAAO;QACP,MAAM;IACR;IACA;QACE,OAAO;QACP,OAAO;QACP,QAAQ;QACR,OAAO;QACP,MAAM;IACR;IACA;QACE,OAAO;QACP,OAAO;QACP,QAAQ;QACR,OAAO;QACP,MAAM;IACR;CACD;AAED,MAAM,eAAe;IACnB;QACE,IAAI;QACJ,SAAS;QACT,MAAM;QACN,QAAQ;QACR,OAAO;QACP,QAAQ;QACR,aAAa;IACf;IACA;QACE,IAAI;QACJ,SAAS;QACT,MAAM;QACN,QAAQ;QACR,OAAO;QACP,QAAQ;QACR,aAAa;IACf;IACA;QACE,IAAI;QACJ,SAAS;QACT,MAAM;QACN,QAAQ;QACR,OAAO;QACP,QAAQ;QACR,aAAa;IACf;IACA;QACE,IAAI;QACJ,SAAS;QACT,MAAM;QACN,QAAQ;QACR,OAAO;QACP,QAAQ;QACR,aAAa;IACf;CACD;AAED,MAAM,eAAe;IACnB;QACE,OAAO;QACP,aAAa;QACb,MAAM;QACN,MAAM;QACN,OAAO;IACT;IACA;QACE,OAAO;QACP,aAAa;QACb,MAAM;QACN,MAAM;QACN,OAAO;IACT;IACA;QACE,OAAO;QACP,aAAa;QACb,MAAM;QACN,MAAM;QACN,OAAO;IACT;IACA;QACE,OAAO;QACP,aAAa;QACb,MAAM;QACN,MAAM;QACN,OAAO;IACT;CACD;AAEM,SAAS;IACd,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACZ,MAAM,GAAG,CAAC,CAAC,qBACV,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;kDAAuB,KAAK,KAAK;;;;;;kDACtD,8OAAC,KAAK,IAAI;wCAAC,WAAU;;;;;;;;;;;;0CAEvB,8OAAC,gIAAA,CAAA,cAAW;;kDACV,8OAAC;wCAAI,WAAU;kDAAsB,KAAK,KAAK;;;;;;kDAC/C,8OAAC;wCAAE,WAAW,CAAC,QAAQ,EACrB,KAAK,KAAK,KAAK,OAAO,mBACtB,KAAK,KAAK,KAAK,SAAS,iBACxB,yBACA;;4CACC,KAAK,KAAK,KAAK,sBAAQ,8OAAC;gDAAW,WAAU;;;;;;4CAC7C,KAAK,KAAK,KAAK,wBAAU,8OAAC;gDAAa,WAAU;;;;;;4CACjD,KAAK,MAAM;;;;;;;;;;;;;;uBAdP,KAAK,KAAK;;;;;;;;;;0BAqBzB,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,8OAAC,gIAAA,CAAA,aAAU;;kDACT,8OAAC,gIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,8OAAC,gIAAA,CAAA,kBAAe;kDAAC;;;;;;;;;;;;0CAInB,8OAAC,gIAAA,CAAA,cAAW;;kDACV,8OAAC;wCAAI,WAAU;kDACZ,aAAa,GAAG,CAAC,CAAC,sBACjB,8OAAC;gDAAmB,WAAU;;kEAC5B,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAG,WAAU;sFAAe,MAAM,OAAO;;;;;;sFAC1C,8OAAC,iIAAA,CAAA,QAAK;4EAAC,SAAQ;sFAAW,MAAM,IAAI;;;;;;;;;;;;8EAEtC,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAK,WAAU;;gFACb,MAAM,MAAM,KAAK,6BAAe,8OAAC;oFAAY,WAAU;;;;;;gFACvD,MAAM,MAAM,KAAK,+BAAiB,8OAAC;oFAAM,WAAU;;;;;;gFACnD,MAAM,MAAM,KAAK,0BAAY,8OAAC;oFAAQ,WAAU;;;;;;gFAChD,MAAM,MAAM;;;;;;;wEAEd,MAAM,WAAW,kBAAI,8OAAC;sFAAM,MAAM,WAAW;;;;;;;;;;;;;;;;;;;;;;;kEAIpD,8OAAC;wDAAI,WAAU;;4DACZ,MAAM,KAAK,kBACV,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFAAyB,MAAM,KAAK;;;;;;kFACnD,8OAAC;wEAAI,WAAU;kFAAgC;;;;;;;;;;;;4DAGlD,MAAM,MAAM,KAAK,sBAChB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFAAyC,MAAM,MAAM;;;;;;kFACpE,8OAAC;wEAAI,WAAU;kFAAgC;;;;;;;;;;;;0EAGnD,8OAAC,kIAAA,CAAA,SAAM;gEAAC,SAAQ;gEAAQ,MAAK;0EAC3B,cAAA,8OAAC;oEAAW,WAAU;;;;;;;;;;;;;;;;;;+CAhClB,MAAM,EAAE;;;;;;;;;;kDAsCtB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,WAAU;sDAAS;;;;;;;;;;;;;;;;;;;;;;;kCAQnD,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;;kDACT,8OAAC,gIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,8OAAC,gIAAA,CAAA,kBAAe;kDAAC;;;;;;;;;;;;0CAInB,8OAAC,gIAAA,CAAA,cAAW;0CACV,cAAA,8OAAC;oCAAI,WAAU;8CACZ,aAAa,GAAG,CAAC,CAAC,uBACjB,8OAAC,4JAAA,CAAA,UAAI;4CAAoB,MAAM,OAAO,IAAI;sDACxC,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAW,CAAC,eAAe,EAAE,OAAO,KAAK,EAAE;kEAC9C,cAAA,8OAAC,OAAO,IAAI;4DAAC,WAAU;;;;;;;;;;;kEAEzB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EAAuB,OAAO,KAAK;;;;;;0EAClD,8OAAC;gEAAI,WAAU;0EAAiC,OAAO,WAAW;;;;;;;;;;;;;;;;;;2CAP7D,OAAO,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAkBjC,8OAAC,gIAAA,CAAA,OAAI;;kCACH,8OAAC,gIAAA,CAAA,aAAU;;0CACT,8OAAC,gIAAA,CAAA,YAAS;0CAAC;;;;;;0CACX,8OAAC,gIAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;kCAInB,8OAAC,gIAAA,CAAA,cAAW;;0CACV,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;kEAAK;;;;;;kEACN,8OAAC;kEAAK;;;;;;;;;;;;0DAER,8OAAC,oIAAA,CAAA,WAAQ;gDAAC,OAAO;gDAAI,WAAU;;;;;;;;;;;;kDAEjC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;kEAAK;;;;;;kEACN,8OAAC;kEAAK;;;;;;;;;;;;0DAER,8OAAC,oIAAA,CAAA,WAAQ;gDAAC,OAAO;gDAAI,WAAU;;;;;;;;;;;;kDAEjC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;kEAAK;;;;;;kEACN,8OAAC;kEAAK;;;;;;;;;;;;0DAER,8OAAC,oIAAA,CAAA,WAAQ;gDAAC,OAAO;gDAAI,WAAU;;;;;;;;;;;;;;;;;;0CAGnC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;kDAAgC;;;;;;kDAG7C,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,MAAK;kDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQhD", "debugId": null}}]}