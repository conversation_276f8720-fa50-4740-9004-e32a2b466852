"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { 
  TrendingUp, 
  TrendingDown, 
  Clock, 
  Smartphone, 
  Globe,
  AlertTriangle,
  CheckCircle,
  Search
} from "@/components/ui/icons"

interface AuditResult {
  url: string
  score: number
  metrics: {
    fcp: number
    lcp: number
    cls: number
    fid: number
    ttfb: number
  }
  opportunities: Array<{
    title: string
    description: string
    impact: "high" | "medium" | "low"
    savings: string
  }>
  diagnostics: Array<{
    title: string
    description: string
    severity: "error" | "warning" | "info"
  }>
}

const mockResults: AuditResult = {
  url: "https://example.com",
  score: 78,
  metrics: {
    fcp: 1.2,
    lcp: 2.1,
    cls: 0.05,
    fid: 45,
    ttfb: 0.8
  },
  opportunities: [
    {
      title: "Eliminate render-blocking resources",
      description: "Resources are blocking the first paint of your page. Consider delivering critical JS/CSS inline and deferring all non-critical JS/styles.",
      impact: "high",
      savings: "1.2s"
    },
    {
      title: "Properly size images",
      description: "Serve images that are appropriately-sized to save cellular data and improve load time.",
      impact: "medium",
      savings: "0.8s"
    },
    {
      title: "Enable text compression",
      description: "Text-based resources should be served with compression (gzip, deflate or brotli) to minimize total network bytes.",
      impact: "low",
      savings: "0.3s"
    }
  ],
  diagnostics: [
    {
      title: "Avoid enormous network payloads",
      description: "Large network payloads cost users real money and are highly correlated with long load times.",
      severity: "warning"
    },
    {
      title: "Serve images in next-gen formats",
      description: "Image formats like WebP and AVIF often provide better compression than PNG or JPEG.",
      severity: "info"
    }
  ]
}

export function PageSpeedAudit() {
  const [url, setUrl] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [results, setResults] = useState<AuditResult | null>(null)

  const handleAudit = async () => {
    if (!url) return
    
    setIsLoading(true)
    // Simulate API call
    setTimeout(() => {
      setResults({ ...mockResults, url })
      setIsLoading(false)
    }, 3000)
  }

  const getScoreColor = (score: number) => {
    if (score >= 90) return "text-green-600"
    if (score >= 50) return "text-yellow-600"
    return "text-red-600"
  }

  const getMetricColor = (metric: string, value: number) => {
    const thresholds: Record<string, { good: number; poor: number }> = {
      fcp: { good: 1.8, poor: 3.0 },
      lcp: { good: 2.5, poor: 4.0 },
      cls: { good: 0.1, poor: 0.25 },
      fid: { good: 100, poor: 300 },
      ttfb: { good: 0.8, poor: 1.8 }
    }
    
    const threshold = thresholds[metric]
    if (!threshold) return "text-gray-600"
    
    if (value <= threshold.good) return "text-green-600"
    if (value <= threshold.poor) return "text-yellow-600"
    return "text-red-600"
  }

  return (
    <div className="space-y-6">
      {/* URL Input */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Search className="h-5 w-5" />
            Start PageSpeed Audit
          </CardTitle>
          <CardDescription>
            Enter a URL to analyze its performance and get optimization recommendations
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4">
            <input
              type="url"
              placeholder="https://example.com"
              value={url}
              onChange={(e) => setUrl(e.target.value)}
              className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <Button 
              onClick={handleAudit} 
              disabled={!url || isLoading}
              className="min-w-[120px]"
            >
              {isLoading ? "Analyzing..." : "Run Audit"}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Loading State */}
      {isLoading && (
        <Card>
          <CardContent className="py-8">
            <div className="text-center space-y-4">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
              <p className="text-gray-600">Analyzing website performance...</p>
              <p className="text-sm text-gray-500">This may take up to 60 seconds</p>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Results */}
      {results && !isLoading && (
        <div className="space-y-6">
          {/* Overall Score */}
          <Card>
            <CardHeader>
              <CardTitle>Performance Score</CardTitle>
              <CardDescription>Overall performance score for {results.url}</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <div className={`text-4xl font-bold ${getScoreColor(results.score)}`}>
                    {results.score}
                  </div>
                  <div>
                    <div className="text-sm text-gray-600">Performance Score</div>
                    <div className="flex items-center gap-2 mt-1">
                      {results.score >= 90 ? (
                        <CheckCircle className="h-4 w-4 text-green-600" />
                      ) : (
                        <AlertTriangle className="h-4 w-4 text-yellow-600" />
                      )}
                      <span className="text-sm">
                        {results.score >= 90 ? "Good" : results.score >= 50 ? "Needs Improvement" : "Poor"}
                      </span>
                    </div>
                  </div>
                </div>
                <div className="flex gap-4">
                  <div className="text-center">
                    <Globe className="h-6 w-6 mx-auto text-gray-600" />
                    <div className="text-xs text-gray-600 mt-1">Desktop</div>
                  </div>
                  <div className="text-center">
                    <Smartphone className="h-6 w-6 mx-auto text-gray-600" />
                    <div className="text-xs text-gray-600 mt-1">Mobile</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Core Web Vitals */}
          <Card>
            <CardHeader>
              <CardTitle>Core Web Vitals</CardTitle>
              <CardDescription>Key metrics that measure user experience</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-4">
                <div className="text-center">
                  <div className={`text-2xl font-bold ${getMetricColor('fcp', results.metrics.fcp)}`}>
                    {results.metrics.fcp}s
                  </div>
                  <div className="text-sm text-gray-600">First Contentful Paint</div>
                </div>
                <div className="text-center">
                  <div className={`text-2xl font-bold ${getMetricColor('lcp', results.metrics.lcp)}`}>
                    {results.metrics.lcp}s
                  </div>
                  <div className="text-sm text-gray-600">Largest Contentful Paint</div>
                </div>
                <div className="text-center">
                  <div className={`text-2xl font-bold ${getMetricColor('cls', results.metrics.cls)}`}>
                    {results.metrics.cls}
                  </div>
                  <div className="text-sm text-gray-600">Cumulative Layout Shift</div>
                </div>
                <div className="text-center">
                  <div className={`text-2xl font-bold ${getMetricColor('fid', results.metrics.fid)}`}>
                    {results.metrics.fid}ms
                  </div>
                  <div className="text-sm text-gray-600">First Input Delay</div>
                </div>
                <div className="text-center">
                  <div className={`text-2xl font-bold ${getMetricColor('ttfb', results.metrics.ttfb)}`}>
                    {results.metrics.ttfb}s
                  </div>
                  <div className="text-sm text-gray-600">Time to First Byte</div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Opportunities */}
          <Card>
            <CardHeader>
              <CardTitle>Opportunities</CardTitle>
              <CardDescription>Suggestions to improve performance</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {results.opportunities.map((opportunity, index) => (
                  <div key={index} className="border rounded-lg p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <h4 className="font-medium">{opportunity.title}</h4>
                          <Badge 
                            variant={opportunity.impact === "high" ? "destructive" : opportunity.impact === "medium" ? "secondary" : "outline"}
                          >
                            {opportunity.impact} impact
                          </Badge>
                        </div>
                        <p className="text-sm text-gray-600">{opportunity.description}</p>
                      </div>
                      <div className="text-right ml-4">
                        <div className="text-lg font-semibold text-green-600">{opportunity.savings}</div>
                        <div className="text-xs text-gray-600">potential savings</div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Diagnostics */}
          <Card>
            <CardHeader>
              <CardTitle>Diagnostics</CardTitle>
              <CardDescription>Additional information about page performance</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {results.diagnostics.map((diagnostic, index) => (
                  <div key={index} className="flex items-start gap-3 p-3 border rounded-lg">
                    {diagnostic.severity === "error" && <AlertTriangle className="h-5 w-5 text-red-600 mt-0.5" />}
                    {diagnostic.severity === "warning" && <AlertTriangle className="h-5 w-5 text-yellow-600 mt-0.5" />}
                    {diagnostic.severity === "info" && <CheckCircle className="h-5 w-5 text-blue-600 mt-0.5" />}
                    <div>
                      <h4 className="font-medium">{diagnostic.title}</h4>
                      <p className="text-sm text-gray-600">{diagnostic.description}</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  )
}
