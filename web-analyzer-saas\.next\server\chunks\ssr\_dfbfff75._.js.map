{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/UI/web-analyzer-saas/src/app/page.tsx"], "sourcesContent": ["import { redirect } from \"next/navigation\";\n\nexport default function Home() {\n  // Redirect to dashboard for authenticated users\n  // In a real app, you'd check authentication status here\n  redirect(\"/dashboard\");\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;;AAEe,SAAS;IACtB,gDAAgD;IAChD,wDAAwD;IACxD,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD,EAAE;AACX", "debugId": null}}]}