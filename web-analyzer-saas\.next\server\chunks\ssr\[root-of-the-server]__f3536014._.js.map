{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/UI/web-analyzer-saas/src/lib/utils.ts"], "sourcesContent": ["export function cn(...classes: (string | undefined | null | false)[]): string {\n  return classes.filter(Boolean).join(\" \");\n}\n\nexport function formatDate(date: Date): string {\n  return new Intl.DateTimeFormat(\"en-US\", {\n    month: \"short\",\n    day: \"numeric\",\n    year: \"numeric\",\n  }).format(date);\n}\n\nexport function formatNumber(num: number): string {\n  return new Intl.NumberFormat(\"en-US\").format(num);\n}\n\nexport function formatPercentage(num: number): string {\n  return new Intl.NumberFormat(\"en-US\", {\n    style: \"percent\",\n    minimumFractionDigits: 1,\n    maximumFractionDigits: 1,\n  }).format(num / 100);\n}\n\nexport function truncateText(text: string, maxLength: number): string {\n  if (text.length <= maxLength) return text;\n  return text.slice(0, maxLength) + \"...\";\n}\n\nexport function getInitials(name: string): string {\n  return name\n    .split(\" \")\n    .map((n) => n[0])\n    .join(\"\")\n    .toUpperCase()\n    .slice(0, 2);\n}\n\nexport function generateId(): string {\n  return Math.random().toString(36).substring(2, 11);\n}\n\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout;\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAAO,SAAS,GAAG,GAAG,OAA8C;IAClE,OAAO,QAAQ,MAAM,CAAC,SAAS,IAAI,CAAC;AACtC;AAEO,SAAS,WAAW,IAAU;IACnC,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,OAAO;QACP,KAAK;QACL,MAAM;IACR,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,aAAa,GAAW;IACtC,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS,MAAM,CAAC;AAC/C;AAEO,SAAS,iBAAiB,GAAW;IAC1C,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,uBAAuB;QACvB,uBAAuB;IACzB,GAAG,MAAM,CAAC,MAAM;AAClB;AAEO,SAAS,aAAa,IAAY,EAAE,SAAiB;IAC1D,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,KAAK,CAAC,GAAG,aAAa;AACpC;AAEO,SAAS,YAAY,IAAY;IACtC,OAAO,KACJ,KAAK,CAAC,KACN,GAAG,CAAC,CAAC,IAAM,CAAC,CAAC,EAAE,EACf,IAAI,CAAC,IACL,WAAW,GACX,KAAK,CAAC,GAAG;AACd;AAEO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG;AACjD;AAEO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF", "debugId": null}}, {"offset": {"line": 92, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/UI/web-analyzer-saas/src/components/ui/icons.tsx"], "sourcesContent": ["import React from \"react\";\n\ninterface IconProps {\n  className?: string;\n  size?: number;\n}\n\nexport const Home = ({ className = \"\", size = 16 }: IconProps) => (\n  <svg\n    className={className}\n    width={size}\n    height={size}\n    viewBox=\"0 0 24 24\"\n    fill=\"none\"\n    stroke=\"currentColor\"\n    strokeWidth=\"2\"\n    strokeLinecap=\"round\"\n    strokeLinejoin=\"round\"\n  >\n    <path d=\"m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z\" />\n    <polyline points=\"9,22 9,12 15,12 15,22\" />\n  </svg>\n);\n\nexport const Activity = ({ className = \"\", size = 16 }: IconProps) => (\n  <svg\n    className={className}\n    width={size}\n    height={size}\n    viewBox=\"0 0 24 24\"\n    fill=\"none\"\n    stroke=\"currentColor\"\n    strokeWidth=\"2\"\n    strokeLinecap=\"round\"\n    strokeLinejoin=\"round\"\n  >\n    <path d=\"M22 12h-4l-3 9L9 3l-3 9H2\" />\n  </svg>\n);\n\nexport const Search = ({ className = \"\", size = 16 }: IconProps) => (\n  <svg\n    className={className}\n    width={size}\n    height={size}\n    viewBox=\"0 0 24 24\"\n    fill=\"none\"\n    stroke=\"currentColor\"\n    strokeWidth=\"2\"\n    strokeLinecap=\"round\"\n    strokeLinejoin=\"round\"\n  >\n    <circle cx=\"11\" cy=\"11\" r=\"8\" />\n    <path d=\"m21 21-4.35-4.35\" />\n  </svg>\n);\n\nexport const Globe = ({ className = \"\", size = 16 }: IconProps) => (\n  <svg\n    className={className}\n    width={size}\n    height={size}\n    viewBox=\"0 0 24 24\"\n    fill=\"none\"\n    stroke=\"currentColor\"\n    strokeWidth=\"2\"\n    strokeLinecap=\"round\"\n    strokeLinejoin=\"round\"\n  >\n    <circle cx=\"12\" cy=\"12\" r=\"10\" />\n    <line x1=\"2\" x2=\"22\" y1=\"12\" y2=\"12\" />\n    <path d=\"M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z\" />\n  </svg>\n);\n\nexport const Users = ({ className = \"\", size = 16 }: IconProps) => (\n  <svg\n    className={className}\n    width={size}\n    height={size}\n    viewBox=\"0 0 24 24\"\n    fill=\"none\"\n    stroke=\"currentColor\"\n    strokeWidth=\"2\"\n    strokeLinecap=\"round\"\n    strokeLinejoin=\"round\"\n  >\n    <path d=\"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2\" />\n    <circle cx=\"9\" cy=\"7\" r=\"4\" />\n    <path d=\"m22 21-3-3\" />\n    <path d=\"m16 16 3 3\" />\n  </svg>\n);\n\nexport const Settings = ({ className = \"\", size = 16 }: IconProps) => (\n  <svg\n    className={className}\n    width={size}\n    height={size}\n    viewBox=\"0 0 24 24\"\n    fill=\"none\"\n    stroke=\"currentColor\"\n    strokeWidth=\"2\"\n    strokeLinecap=\"round\"\n    strokeLinejoin=\"round\"\n  >\n    <path d=\"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z\" />\n    <circle cx=\"12\" cy=\"12\" r=\"3\" />\n  </svg>\n);\n\nexport const CreditCard = ({ className = \"\", size = 16 }: IconProps) => (\n  <svg\n    className={className}\n    width={size}\n    height={size}\n    viewBox=\"0 0 24 24\"\n    fill=\"none\"\n    stroke=\"currentColor\"\n    strokeWidth=\"2\"\n    strokeLinecap=\"round\"\n    strokeLinejoin=\"round\"\n  >\n    <rect width=\"20\" height=\"14\" x=\"2\" y=\"5\" rx=\"2\" />\n    <line x1=\"2\" x2=\"22\" y1=\"10\" y2=\"10\" />\n  </svg>\n);\n\nexport const FileText = ({ className = \"\", size = 16 }: IconProps) => (\n  <svg\n    className={className}\n    width={size}\n    height={size}\n    viewBox=\"0 0 24 24\"\n    fill=\"none\"\n    stroke=\"currentColor\"\n    strokeWidth=\"2\"\n    strokeLinecap=\"round\"\n    strokeLinejoin=\"round\"\n  >\n    <path d=\"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z\" />\n    <polyline points=\"14,2 14,8 20,8\" />\n    <line x1=\"16\" x2=\"8\" y1=\"13\" y2=\"13\" />\n    <line x1=\"16\" x2=\"8\" y1=\"17\" y2=\"17\" />\n    <line x1=\"10\" x2=\"8\" y1=\"9\" y2=\"9\" />\n  </svg>\n);\n\nexport const Bot = ({ className = \"\", size = 16 }: IconProps) => (\n  <svg\n    className={className}\n    width={size}\n    height={size}\n    viewBox=\"0 0 24 24\"\n    fill=\"none\"\n    stroke=\"currentColor\"\n    strokeWidth=\"2\"\n    strokeLinecap=\"round\"\n    strokeLinejoin=\"round\"\n  >\n    <path d=\"M12 8V4H8\" />\n    <rect width=\"16\" height=\"12\" x=\"4\" y=\"8\" rx=\"2\" />\n    <path d=\"m9 16 0 0\" />\n    <path d=\"m15 16 0 0\" />\n  </svg>\n);\n\nexport const ChevronDown = ({ className = \"\", size = 16 }: IconProps) => (\n  <svg\n    className={className}\n    width={size}\n    height={size}\n    viewBox=\"0 0 24 24\"\n    fill=\"none\"\n    stroke=\"currentColor\"\n    strokeWidth=\"2\"\n    strokeLinecap=\"round\"\n    strokeLinejoin=\"round\"\n  >\n    <path d=\"m6 9 6 6 6-6\" />\n  </svg>\n);\n\nexport const ChevronRight = ({ className = \"\", size = 16 }: IconProps) => (\n  <svg\n    className={className}\n    width={size}\n    height={size}\n    viewBox=\"0 0 24 24\"\n    fill=\"none\"\n    stroke=\"currentColor\"\n    strokeWidth=\"2\"\n    strokeLinecap=\"round\"\n    strokeLinejoin=\"round\"\n  >\n    <path d=\"m9 18 6-6-6-6\" />\n  </svg>\n);\n\nexport const Bell = ({ className = \"\", size = 16 }: IconProps) => (\n  <svg\n    className={className}\n    width={size}\n    height={size}\n    viewBox=\"0 0 24 24\"\n    fill=\"none\"\n    stroke=\"currentColor\"\n    strokeWidth=\"2\"\n    strokeLinecap=\"round\"\n    strokeLinejoin=\"round\"\n  >\n    <path d=\"M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9\" />\n    <path d=\"M10.3 21a1.94 1.94 0 0 0 3.4 0\" />\n  </svg>\n);\n\nexport const Menu = ({ className = \"\", size = 16 }: IconProps) => (\n  <svg\n    className={className}\n    width={size}\n    height={size}\n    viewBox=\"0 0 24 24\"\n    fill=\"none\"\n    stroke=\"currentColor\"\n    strokeWidth=\"2\"\n    strokeLinecap=\"round\"\n    strokeLinejoin=\"round\"\n  >\n    <line x1=\"4\" x2=\"20\" y1=\"12\" y2=\"12\" />\n    <line x1=\"4\" x2=\"20\" y1=\"6\" y2=\"6\" />\n    <line x1=\"4\" x2=\"20\" y1=\"18\" y2=\"18\" />\n  </svg>\n);\n\nexport const X = ({ className = \"\", size = 16 }: IconProps) => (\n  <svg\n    className={className}\n    width={size}\n    height={size}\n    viewBox=\"0 0 24 24\"\n    fill=\"none\"\n    stroke=\"currentColor\"\n    strokeWidth=\"2\"\n    strokeLinecap=\"round\"\n    strokeLinejoin=\"round\"\n  >\n    <path d=\"M18 6 6 18\" />\n    <path d=\"m6 6 12 12\" />\n  </svg>\n);\n\nexport const User = ({ className = \"\", size = 16 }: IconProps) => (\n  <svg\n    className={className}\n    width={size}\n    height={size}\n    viewBox=\"0 0 24 24\"\n    fill=\"none\"\n    stroke=\"currentColor\"\n    strokeWidth=\"2\"\n    strokeLinecap=\"round\"\n    strokeLinejoin=\"round\"\n  >\n    <path d=\"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2\" />\n    <circle cx=\"12\" cy=\"7\" r=\"4\" />\n  </svg>\n);\n\nexport const LogOut = ({ className = \"\", size = 16 }: IconProps) => (\n  <svg\n    className={className}\n    width={size}\n    height={size}\n    viewBox=\"0 0 24 24\"\n    fill=\"none\"\n    stroke=\"currentColor\"\n    strokeWidth=\"2\"\n    strokeLinecap=\"round\"\n    strokeLinejoin=\"round\"\n  >\n    <path d=\"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4\" />\n    <polyline points=\"16,17 21,12 16,7\" />\n    <line x1=\"21\" x2=\"9\" y1=\"12\" y2=\"12\" />\n  </svg>\n);\n\nexport const HelpCircle = ({ className = \"\", size = 16 }: IconProps) => (\n  <svg\n    className={className}\n    width={size}\n    height={size}\n    viewBox=\"0 0 24 24\"\n    fill=\"none\"\n    stroke=\"currentColor\"\n    strokeWidth=\"2\"\n    strokeLinecap=\"round\"\n    strokeLinejoin=\"round\"\n  >\n    <circle cx=\"12\" cy=\"12\" r=\"10\" />\n    <path d=\"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3\" />\n    <path d=\"M12 17h.01\" />\n  </svg>\n);\n\nexport const BarChart3 = ({ className = \"\", size = 16 }: IconProps) => (\n  <svg\n    className={className}\n    width={size}\n    height={size}\n    viewBox=\"0 0 24 24\"\n    fill=\"none\"\n    stroke=\"currentColor\"\n    strokeWidth=\"2\"\n    strokeLinecap=\"round\"\n    strokeLinejoin=\"round\"\n  >\n    <path d=\"M3 3v18h18\" />\n    <path d=\"M18 17V9\" />\n    <path d=\"M13 17V5\" />\n    <path d=\"M8 17v-3\" />\n  </svg>\n);\n\nexport const TrendingUp = ({ className = \"\", size = 16 }: IconProps) => (\n  <svg\n    className={className}\n    width={size}\n    height={size}\n    viewBox=\"0 0 24 24\"\n    fill=\"none\"\n    stroke=\"currentColor\"\n    strokeWidth=\"2\"\n    strokeLinecap=\"round\"\n    strokeLinejoin=\"round\"\n  >\n    <polyline points=\"22,7 13.5,15.5 8.5,10.5 2,17\" />\n    <polyline points=\"16,7 22,7 22,13\" />\n  </svg>\n);\n\nexport const TrendingDown = ({ className = \"\", size = 16 }: IconProps) => (\n  <svg\n    className={className}\n    width={size}\n    height={size}\n    viewBox=\"0 0 24 24\"\n    fill=\"none\"\n    stroke=\"currentColor\"\n    strokeWidth=\"2\"\n    strokeLinecap=\"round\"\n    strokeLinejoin=\"round\"\n  >\n    <polyline points=\"22,17 13.5,8.5 8.5,13.5 2,7\" />\n    <polyline points=\"16,17 22,17 22,11\" />\n  </svg>\n);\n\nexport const CheckCircle = ({ className = \"\", size = 16 }: IconProps) => (\n  <svg\n    className={className}\n    width={size}\n    height={size}\n    viewBox=\"0 0 24 24\"\n    fill=\"none\"\n    stroke=\"currentColor\"\n    strokeWidth=\"2\"\n    strokeLinecap=\"round\"\n    strokeLinejoin=\"round\"\n  >\n    <path d=\"M22 11.08V12a10 10 0 1 1-5.93-9.14\" />\n    <polyline points=\"22,4 12,14.01 9,11.01\" />\n  </svg>\n);\n\nexport const AlertTriangle = ({ className = \"\", size = 16 }: IconProps) => (\n  <svg\n    className={className}\n    width={size}\n    height={size}\n    viewBox=\"0 0 24 24\"\n    fill=\"none\"\n    stroke=\"currentColor\"\n    strokeWidth=\"2\"\n    strokeLinecap=\"round\"\n    strokeLinejoin=\"round\"\n  >\n    <path d=\"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z\" />\n    <path d=\"M12 9v4\" />\n    <path d=\"M12 17h.01\" />\n  </svg>\n);\n\nexport const XCircle = ({ className = \"\", size = 16 }: IconProps) => (\n  <svg\n    className={className}\n    width={size}\n    height={size}\n    viewBox=\"0 0 24 24\"\n    fill=\"none\"\n    stroke=\"currentColor\"\n    strokeWidth=\"2\"\n    strokeLinecap=\"round\"\n    strokeLinejoin=\"round\"\n  >\n    <circle cx=\"12\" cy=\"12\" r=\"10\" />\n    <path d=\"m15 9-6 6\" />\n    <path d=\"m9 9 6 6\" />\n  </svg>\n);\n\nexport const Clock = ({ className = \"\", size = 16 }: IconProps) => (\n  <svg\n    className={className}\n    width={size}\n    height={size}\n    viewBox=\"0 0 24 24\"\n    fill=\"none\"\n    stroke=\"currentColor\"\n    strokeWidth=\"2\"\n    strokeLinecap=\"round\"\n    strokeLinejoin=\"round\"\n  >\n    <circle cx=\"12\" cy=\"12\" r=\"10\" />\n    <polyline points=\"12,6 12,12 16,14\" />\n  </svg>\n);\n\nexport const ArrowRight = ({ className = \"\", size = 16 }: IconProps) => (\n  <svg\n    className={className}\n    width={size}\n    height={size}\n    viewBox=\"0 0 24 24\"\n    fill=\"none\"\n    stroke=\"currentColor\"\n    strokeWidth=\"2\"\n    strokeLinecap=\"round\"\n    strokeLinejoin=\"round\"\n  >\n    <path d=\"M5 12h14\" />\n    <path d=\"m12 5 7 7-7 7\" />\n  </svg>\n);\n\nexport const Zap = ({ className = \"\", size = 16 }: IconProps) => (\n  <svg\n    className={className}\n    width={size}\n    height={size}\n    viewBox=\"0 0 24 24\"\n    fill=\"none\"\n    stroke=\"currentColor\"\n    strokeWidth=\"2\"\n    strokeLinecap=\"round\"\n    strokeLinejoin=\"round\"\n  >\n    <polygon points=\"13,2 3,14 12,14 11,22 21,10 12,10\" />\n  </svg>\n);\n\nexport const Target = ({ className = \"\", size = 16 }: IconProps) => (\n  <svg\n    className={className}\n    width={size}\n    height={size}\n    viewBox=\"0 0 24 24\"\n    fill=\"none\"\n    stroke=\"currentColor\"\n    strokeWidth=\"2\"\n    strokeLinecap=\"round\"\n    strokeLinejoin=\"round\"\n  >\n    <circle cx=\"12\" cy=\"12\" r=\"10\" />\n    <circle cx=\"12\" cy=\"12\" r=\"6\" />\n    <circle cx=\"12\" cy=\"12\" r=\"2\" />\n  </svg>\n);\n\nexport const Shield = ({ className = \"\", size = 16 }: IconProps) => (\n  <svg\n    className={className}\n    width={size}\n    height={size}\n    viewBox=\"0 0 24 24\"\n    fill=\"none\"\n    stroke=\"currentColor\"\n    strokeWidth=\"2\"\n    strokeLinecap=\"round\"\n    strokeLinejoin=\"round\"\n  >\n    <path d=\"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z\" />\n  </svg>\n);\n\nexport const LinkIcon = ({ className = \"\", size = 16 }: IconProps) => (\n  <svg\n    className={className}\n    width={size}\n    height={size}\n    viewBox=\"0 0 24 24\"\n    fill=\"none\"\n    stroke=\"currentColor\"\n    strokeWidth=\"2\"\n    strokeLinecap=\"round\"\n    strokeLinejoin=\"round\"\n  >\n    <path d=\"M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71\" />\n    <path d=\"M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71\" />\n  </svg>\n);\n\nexport const Smartphone = ({ className = \"\", size = 16 }: IconProps) => (\n  <svg\n    className={className}\n    width={size}\n    height={size}\n    viewBox=\"0 0 24 24\"\n    fill=\"none\"\n    stroke=\"currentColor\"\n    strokeWidth=\"2\"\n    strokeLinecap=\"round\"\n    strokeLinejoin=\"round\"\n  >\n    <rect width=\"14\" height=\"20\" x=\"5\" y=\"2\" rx=\"2\" ry=\"2\" />\n    <line x1=\"12\" x2=\"12.01\" y1=\"18\" y2=\"18\" />\n  </svg>\n);\n\nexport const Code = ({ className = \"\", size = 16 }: IconProps) => (\n  <svg\n    className={className}\n    width={size}\n    height={size}\n    viewBox=\"0 0 24 24\"\n    fill=\"none\"\n    stroke=\"currentColor\"\n    strokeWidth=\"2\"\n    strokeLinecap=\"round\"\n    strokeLinejoin=\"round\"\n  >\n    <polyline points=\"16,18 22,12 16,6\" />\n    <polyline points=\"8,6 2,12 8,18\" />\n  </svg>\n);\n\nexport const Eye = ({ className = \"\", size = 16 }: IconProps) => (\n  <svg\n    className={className}\n    width={size}\n    height={size}\n    viewBox=\"0 0 24 24\"\n    fill=\"none\"\n    stroke=\"currentColor\"\n    strokeWidth=\"2\"\n    strokeLinecap=\"round\"\n    strokeLinejoin=\"round\"\n  >\n    <path d=\"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z\" />\n    <circle cx=\"12\" cy=\"12\" r=\"3\" />\n  </svg>\n);\n\nexport const Puzzle = ({ className = \"\", size = 16 }: IconProps) => (\n  <svg\n    className={className}\n    width={size}\n    height={size}\n    viewBox=\"0 0 24 24\"\n    fill=\"none\"\n    stroke=\"currentColor\"\n    strokeWidth=\"2\"\n    strokeLinecap=\"round\"\n    strokeLinejoin=\"round\"\n  >\n    <path d=\"M19.439 7.85c-.049.322-.059.648-.026.975.056.506.194.999.416 1.452.221.453.52.865.896 1.218.375.353.82.635 1.298.82.478.185.985.281 1.496.281.511 0 1.018-.096 1.496-.281.478-.185.923-.467 1.298-.82.375-.353.675-.765.896-1.218.222-.453.36-.946.416-1.452.033-.327.023-.653-.026-.975-.05-.322-.142-.637-.272-.936-.13-.299-.298-.576-.5-.822-.202-.246-.438-.46-.698-.635-.26-.175-.543-.31-.84-.4-.297-.09-.605-.135-.914-.135-.309 0-.617.045-.914.135-.297.09-.58.225-.84.4-.26.175-.496.389-.698.635-.202.246-.37.523-.5.822-.13.299-.222.614-.272.936z\" />\n    <path d=\"M14 14.5c0-.83-.67-1.5-1.5-1.5S11 13.67 11 14.5 11.67 16 12.5 16 14 15.33 14 14.5z\" />\n  </svg>\n);\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOO,MAAM,OAAO,CAAC,EAAE,YAAY,EAAE,EAAE,OAAO,EAAE,EAAa,iBAC3D,8OAAC;QACC,WAAW;QACX,OAAO;QACP,QAAQ;QACR,SAAQ;QACR,MAAK;QACL,QAAO;QACP,aAAY;QACZ,eAAc;QACd,gBAAe;;0BAEf,8OAAC;gBAAK,GAAE;;;;;;0BACR,8OAAC;gBAAS,QAAO;;;;;;;;;;;;AAId,MAAM,WAAW,CAAC,EAAE,YAAY,EAAE,EAAE,OAAO,EAAE,EAAa,iBAC/D,8OAAC;QACC,WAAW;QACX,OAAO;QACP,QAAQ;QACR,SAAQ;QACR,MAAK;QACL,QAAO;QACP,aAAY;QACZ,eAAc;QACd,gBAAe;kBAEf,cAAA,8OAAC;YAAK,GAAE;;;;;;;;;;;AAIL,MAAM,SAAS,CAAC,EAAE,YAAY,EAAE,EAAE,OAAO,EAAE,EAAa,iBAC7D,8OAAC;QACC,WAAW;QACX,OAAO;QACP,QAAQ;QACR,SAAQ;QACR,MAAK;QACL,QAAO;QACP,aAAY;QACZ,eAAc;QACd,gBAAe;;0BAEf,8OAAC;gBAAO,IAAG;gBAAK,IAAG;gBAAK,GAAE;;;;;;0BAC1B,8OAAC;gBAAK,GAAE;;;;;;;;;;;;AAIL,MAAM,QAAQ,CAAC,EAAE,YAAY,EAAE,EAAE,OAAO,EAAE,EAAa,iBAC5D,8OAAC;QACC,WAAW;QACX,OAAO;QACP,QAAQ;QACR,SAAQ;QACR,MAAK;QACL,QAAO;QACP,aAAY;QACZ,eAAc;QACd,gBAAe;;0BAEf,8OAAC;gBAAO,IAAG;gBAAK,IAAG;gBAAK,GAAE;;;;;;0BAC1B,8OAAC;gBAAK,IAAG;gBAAI,IAAG;gBAAK,IAAG;gBAAK,IAAG;;;;;;0BAChC,8OAAC;gBAAK,GAAE;;;;;;;;;;;;AAIL,MAAM,QAAQ,CAAC,EAAE,YAAY,EAAE,EAAE,OAAO,EAAE,EAAa,iBAC5D,8OAAC;QACC,WAAW;QACX,OAAO;QACP,QAAQ;QACR,SAAQ;QACR,MAAK;QACL,QAAO;QACP,aAAY;QACZ,eAAc;QACd,gBAAe;;0BAEf,8OAAC;gBAAK,GAAE;;;;;;0BACR,8OAAC;gBAAO,IAAG;gBAAI,IAAG;gBAAI,GAAE;;;;;;0BACxB,8OAAC;gBAAK,GAAE;;;;;;0BACR,8OAAC;gBAAK,GAAE;;;;;;;;;;;;AAIL,MAAM,WAAW,CAAC,EAAE,YAAY,EAAE,EAAE,OAAO,EAAE,EAAa,iBAC/D,8OAAC;QACC,WAAW;QACX,OAAO;QACP,QAAQ;QACR,SAAQ;QACR,MAAK;QACL,QAAO;QACP,aAAY;QACZ,eAAc;QACd,gBAAe;;0BAEf,8OAAC;gBAAK,GAAE;;;;;;0BACR,8OAAC;gBAAO,IAAG;gBAAK,IAAG;gBAAK,GAAE;;;;;;;;;;;;AAIvB,MAAM,aAAa,CAAC,EAAE,YAAY,EAAE,EAAE,OAAO,EAAE,EAAa,iBACjE,8OAAC;QACC,WAAW;QACX,OAAO;QACP,QAAQ;QACR,SAAQ;QACR,MAAK;QACL,QAAO;QACP,aAAY;QACZ,eAAc;QACd,gBAAe;;0BAEf,8OAAC;gBAAK,OAAM;gBAAK,QAAO;gBAAK,GAAE;gBAAI,GAAE;gBAAI,IAAG;;;;;;0BAC5C,8OAAC;gBAAK,IAAG;gBAAI,IAAG;gBAAK,IAAG;gBAAK,IAAG;;;;;;;;;;;;AAI7B,MAAM,WAAW,CAAC,EAAE,YAAY,EAAE,EAAE,OAAO,EAAE,EAAa,iBAC/D,8OAAC;QACC,WAAW;QACX,OAAO;QACP,QAAQ;QACR,SAAQ;QACR,MAAK;QACL,QAAO;QACP,aAAY;QACZ,eAAc;QACd,gBAAe;;0BAEf,8OAAC;gBAAK,GAAE;;;;;;0BACR,8OAAC;gBAAS,QAAO;;;;;;0BACjB,8OAAC;gBAAK,IAAG;gBAAK,IAAG;gBAAI,IAAG;gBAAK,IAAG;;;;;;0BAChC,8OAAC;gBAAK,IAAG;gBAAK,IAAG;gBAAI,IAAG;gBAAK,IAAG;;;;;;0BAChC,8OAAC;gBAAK,IAAG;gBAAK,IAAG;gBAAI,IAAG;gBAAI,IAAG;;;;;;;;;;;;AAI5B,MAAM,MAAM,CAAC,EAAE,YAAY,EAAE,EAAE,OAAO,EAAE,EAAa,iBAC1D,8OAAC;QACC,WAAW;QACX,OAAO;QACP,QAAQ;QACR,SAAQ;QACR,MAAK;QACL,QAAO;QACP,aAAY;QACZ,eAAc;QACd,gBAAe;;0BAEf,8OAAC;gBAAK,GAAE;;;;;;0BACR,8OAAC;gBAAK,OAAM;gBAAK,QAAO;gBAAK,GAAE;gBAAI,GAAE;gBAAI,IAAG;;;;;;0BAC5C,8OAAC;gBAAK,GAAE;;;;;;0BACR,8OAAC;gBAAK,GAAE;;;;;;;;;;;;AAIL,MAAM,cAAc,CAAC,EAAE,YAAY,EAAE,EAAE,OAAO,EAAE,EAAa,iBAClE,8OAAC;QACC,WAAW;QACX,OAAO;QACP,QAAQ;QACR,SAAQ;QACR,MAAK;QACL,QAAO;QACP,aAAY;QACZ,eAAc;QACd,gBAAe;kBAEf,cAAA,8OAAC;YAAK,GAAE;;;;;;;;;;;AAIL,MAAM,eAAe,CAAC,EAAE,YAAY,EAAE,EAAE,OAAO,EAAE,EAAa,iBACnE,8OAAC;QACC,WAAW;QACX,OAAO;QACP,QAAQ;QACR,SAAQ;QACR,MAAK;QACL,QAAO;QACP,aAAY;QACZ,eAAc;QACd,gBAAe;kBAEf,cAAA,8OAAC;YAAK,GAAE;;;;;;;;;;;AAIL,MAAM,OAAO,CAAC,EAAE,YAAY,EAAE,EAAE,OAAO,EAAE,EAAa,iBAC3D,8OAAC;QACC,WAAW;QACX,OAAO;QACP,QAAQ;QACR,SAAQ;QACR,MAAK;QACL,QAAO;QACP,aAAY;QACZ,eAAc;QACd,gBAAe;;0BAEf,8OAAC;gBAAK,GAAE;;;;;;0BACR,8OAAC;gBAAK,GAAE;;;;;;;;;;;;AAIL,MAAM,OAAO,CAAC,EAAE,YAAY,EAAE,EAAE,OAAO,EAAE,EAAa,iBAC3D,8OAAC;QACC,WAAW;QACX,OAAO;QACP,QAAQ;QACR,SAAQ;QACR,MAAK;QACL,QAAO;QACP,aAAY;QACZ,eAAc;QACd,gBAAe;;0BAEf,8OAAC;gBAAK,IAAG;gBAAI,IAAG;gBAAK,IAAG;gBAAK,IAAG;;;;;;0BAChC,8OAAC;gBAAK,IAAG;gBAAI,IAAG;gBAAK,IAAG;gBAAI,IAAG;;;;;;0BAC/B,8OAAC;gBAAK,IAAG;gBAAI,IAAG;gBAAK,IAAG;gBAAK,IAAG;;;;;;;;;;;;AAI7B,MAAM,IAAI,CAAC,EAAE,YAAY,EAAE,EAAE,OAAO,EAAE,EAAa,iBACxD,8OAAC;QACC,WAAW;QACX,OAAO;QACP,QAAQ;QACR,SAAQ;QACR,MAAK;QACL,QAAO;QACP,aAAY;QACZ,eAAc;QACd,gBAAe;;0BAEf,8OAAC;gBAAK,GAAE;;;;;;0BACR,8OAAC;gBAAK,GAAE;;;;;;;;;;;;AAIL,MAAM,OAAO,CAAC,EAAE,YAAY,EAAE,EAAE,OAAO,EAAE,EAAa,iBAC3D,8OAAC;QACC,WAAW;QACX,OAAO;QACP,QAAQ;QACR,SAAQ;QACR,MAAK;QACL,QAAO;QACP,aAAY;QACZ,eAAc;QACd,gBAAe;;0BAEf,8OAAC;gBAAK,GAAE;;;;;;0BACR,8OAAC;gBAAO,IAAG;gBAAK,IAAG;gBAAI,GAAE;;;;;;;;;;;;AAItB,MAAM,SAAS,CAAC,EAAE,YAAY,EAAE,EAAE,OAAO,EAAE,EAAa,iBAC7D,8OAAC;QACC,WAAW;QACX,OAAO;QACP,QAAQ;QACR,SAAQ;QACR,MAAK;QACL,QAAO;QACP,aAAY;QACZ,eAAc;QACd,gBAAe;;0BAEf,8OAAC;gBAAK,GAAE;;;;;;0BACR,8OAAC;gBAAS,QAAO;;;;;;0BACjB,8OAAC;gBAAK,IAAG;gBAAK,IAAG;gBAAI,IAAG;gBAAK,IAAG;;;;;;;;;;;;AAI7B,MAAM,aAAa,CAAC,EAAE,YAAY,EAAE,EAAE,OAAO,EAAE,EAAa,iBACjE,8OAAC;QACC,WAAW;QACX,OAAO;QACP,QAAQ;QACR,SAAQ;QACR,MAAK;QACL,QAAO;QACP,aAAY;QACZ,eAAc;QACd,gBAAe;;0BAEf,8OAAC;gBAAO,IAAG;gBAAK,IAAG;gBAAK,GAAE;;;;;;0BAC1B,8OAAC;gBAAK,GAAE;;;;;;0BACR,8OAAC;gBAAK,GAAE;;;;;;;;;;;;AAIL,MAAM,YAAY,CAAC,EAAE,YAAY,EAAE,EAAE,OAAO,EAAE,EAAa,iBAChE,8OAAC;QACC,WAAW;QACX,OAAO;QACP,QAAQ;QACR,SAAQ;QACR,MAAK;QACL,QAAO;QACP,aAAY;QACZ,eAAc;QACd,gBAAe;;0BAEf,8OAAC;gBAAK,GAAE;;;;;;0BACR,8OAAC;gBAAK,GAAE;;;;;;0BACR,8OAAC;gBAAK,GAAE;;;;;;0BACR,8OAAC;gBAAK,GAAE;;;;;;;;;;;;AAIL,MAAM,aAAa,CAAC,EAAE,YAAY,EAAE,EAAE,OAAO,EAAE,EAAa,iBACjE,8OAAC;QACC,WAAW;QACX,OAAO;QACP,QAAQ;QACR,SAAQ;QACR,MAAK;QACL,QAAO;QACP,aAAY;QACZ,eAAc;QACd,gBAAe;;0BAEf,8OAAC;gBAAS,QAAO;;;;;;0BACjB,8OAAC;gBAAS,QAAO;;;;;;;;;;;;AAId,MAAM,eAAe,CAAC,EAAE,YAAY,EAAE,EAAE,OAAO,EAAE,EAAa,iBACnE,8OAAC;QACC,WAAW;QACX,OAAO;QACP,QAAQ;QACR,SAAQ;QACR,MAAK;QACL,QAAO;QACP,aAAY;QACZ,eAAc;QACd,gBAAe;;0BAEf,8OAAC;gBAAS,QAAO;;;;;;0BACjB,8OAAC;gBAAS,QAAO;;;;;;;;;;;;AAId,MAAM,cAAc,CAAC,EAAE,YAAY,EAAE,EAAE,OAAO,EAAE,EAAa,iBAClE,8OAAC;QACC,WAAW;QACX,OAAO;QACP,QAAQ;QACR,SAAQ;QACR,MAAK;QACL,QAAO;QACP,aAAY;QACZ,eAAc;QACd,gBAAe;;0BAEf,8OAAC;gBAAK,GAAE;;;;;;0BACR,8OAAC;gBAAS,QAAO;;;;;;;;;;;;AAId,MAAM,gBAAgB,CAAC,EAAE,YAAY,EAAE,EAAE,OAAO,EAAE,EAAa,iBACpE,8OAAC;QACC,WAAW;QACX,OAAO;QACP,QAAQ;QACR,SAAQ;QACR,MAAK;QACL,QAAO;QACP,aAAY;QACZ,eAAc;QACd,gBAAe;;0BAEf,8OAAC;gBAAK,GAAE;;;;;;0BACR,8OAAC;gBAAK,GAAE;;;;;;0BACR,8OAAC;gBAAK,GAAE;;;;;;;;;;;;AAIL,MAAM,UAAU,CAAC,EAAE,YAAY,EAAE,EAAE,OAAO,EAAE,EAAa,iBAC9D,8OAAC;QACC,WAAW;QACX,OAAO;QACP,QAAQ;QACR,SAAQ;QACR,MAAK;QACL,QAAO;QACP,aAAY;QACZ,eAAc;QACd,gBAAe;;0BAEf,8OAAC;gBAAO,IAAG;gBAAK,IAAG;gBAAK,GAAE;;;;;;0BAC1B,8OAAC;gBAAK,GAAE;;;;;;0BACR,8OAAC;gBAAK,GAAE;;;;;;;;;;;;AAIL,MAAM,QAAQ,CAAC,EAAE,YAAY,EAAE,EAAE,OAAO,EAAE,EAAa,iBAC5D,8OAAC;QACC,WAAW;QACX,OAAO;QACP,QAAQ;QACR,SAAQ;QACR,MAAK;QACL,QAAO;QACP,aAAY;QACZ,eAAc;QACd,gBAAe;;0BAEf,8OAAC;gBAAO,IAAG;gBAAK,IAAG;gBAAK,GAAE;;;;;;0BAC1B,8OAAC;gBAAS,QAAO;;;;;;;;;;;;AAId,MAAM,aAAa,CAAC,EAAE,YAAY,EAAE,EAAE,OAAO,EAAE,EAAa,iBACjE,8OAAC;QACC,WAAW;QACX,OAAO;QACP,QAAQ;QACR,SAAQ;QACR,MAAK;QACL,QAAO;QACP,aAAY;QACZ,eAAc;QACd,gBAAe;;0BAEf,8OAAC;gBAAK,GAAE;;;;;;0BACR,8OAAC;gBAAK,GAAE;;;;;;;;;;;;AAIL,MAAM,MAAM,CAAC,EAAE,YAAY,EAAE,EAAE,OAAO,EAAE,EAAa,iBAC1D,8OAAC;QACC,WAAW;QACX,OAAO;QACP,QAAQ;QACR,SAAQ;QACR,MAAK;QACL,QAAO;QACP,aAAY;QACZ,eAAc;QACd,gBAAe;kBAEf,cAAA,8OAAC;YAAQ,QAAO;;;;;;;;;;;AAIb,MAAM,SAAS,CAAC,EAAE,YAAY,EAAE,EAAE,OAAO,EAAE,EAAa,iBAC7D,8OAAC;QACC,WAAW;QACX,OAAO;QACP,QAAQ;QACR,SAAQ;QACR,MAAK;QACL,QAAO;QACP,aAAY;QACZ,eAAc;QACd,gBAAe;;0BAEf,8OAAC;gBAAO,IAAG;gBAAK,IAAG;gBAAK,GAAE;;;;;;0BAC1B,8OAAC;gBAAO,IAAG;gBAAK,IAAG;gBAAK,GAAE;;;;;;0BAC1B,8OAAC;gBAAO,IAAG;gBAAK,IAAG;gBAAK,GAAE;;;;;;;;;;;;AAIvB,MAAM,SAAS,CAAC,EAAE,YAAY,EAAE,EAAE,OAAO,EAAE,EAAa,iBAC7D,8OAAC;QACC,WAAW;QACX,OAAO;QACP,QAAQ;QACR,SAAQ;QACR,MAAK;QACL,QAAO;QACP,aAAY;QACZ,eAAc;QACd,gBAAe;kBAEf,cAAA,8OAAC;YAAK,GAAE;;;;;;;;;;;AAIL,MAAM,WAAW,CAAC,EAAE,YAAY,EAAE,EAAE,OAAO,EAAE,EAAa,iBAC/D,8OAAC;QACC,WAAW;QACX,OAAO;QACP,QAAQ;QACR,SAAQ;QACR,MAAK;QACL,QAAO;QACP,aAAY;QACZ,eAAc;QACd,gBAAe;;0BAEf,8OAAC;gBAAK,GAAE;;;;;;0BACR,8OAAC;gBAAK,GAAE;;;;;;;;;;;;AAIL,MAAM,aAAa,CAAC,EAAE,YAAY,EAAE,EAAE,OAAO,EAAE,EAAa,iBACjE,8OAAC;QACC,WAAW;QACX,OAAO;QACP,QAAQ;QACR,SAAQ;QACR,MAAK;QACL,QAAO;QACP,aAAY;QACZ,eAAc;QACd,gBAAe;;0BAEf,8OAAC;gBAAK,OAAM;gBAAK,QAAO;gBAAK,GAAE;gBAAI,GAAE;gBAAI,IAAG;gBAAI,IAAG;;;;;;0BACnD,8OAAC;gBAAK,IAAG;gBAAK,IAAG;gBAAQ,IAAG;gBAAK,IAAG;;;;;;;;;;;;AAIjC,MAAM,OAAO,CAAC,EAAE,YAAY,EAAE,EAAE,OAAO,EAAE,EAAa,iBAC3D,8OAAC;QACC,WAAW;QACX,OAAO;QACP,QAAQ;QACR,SAAQ;QACR,MAAK;QACL,QAAO;QACP,aAAY;QACZ,eAAc;QACd,gBAAe;;0BAEf,8OAAC;gBAAS,QAAO;;;;;;0BACjB,8OAAC;gBAAS,QAAO;;;;;;;;;;;;AAId,MAAM,MAAM,CAAC,EAAE,YAAY,EAAE,EAAE,OAAO,EAAE,EAAa,iBAC1D,8OAAC;QACC,WAAW;QACX,OAAO;QACP,QAAQ;QACR,SAAQ;QACR,MAAK;QACL,QAAO;QACP,aAAY;QACZ,eAAc;QACd,gBAAe;;0BAEf,8OAAC;gBAAK,GAAE;;;;;;0BACR,8OAAC;gBAAO,IAAG;gBAAK,IAAG;gBAAK,GAAE;;;;;;;;;;;;AAIvB,MAAM,SAAS,CAAC,EAAE,YAAY,EAAE,EAAE,OAAO,EAAE,EAAa,iBAC7D,8OAAC;QACC,WAAW;QACX,OAAO;QACP,QAAQ;QACR,SAAQ;QACR,MAAK;QACL,QAAO;QACP,aAAY;QACZ,eAAc;QACd,gBAAe;;0BAEf,8OAAC;gBAAK,GAAE;;;;;;0BACR,8OAAC;gBAAK,GAAE", "debugId": null}}, {"offset": {"line": 1292, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/UI/web-analyzer-saas/src/components/layout/sidebar.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\nimport <PERSON> from \"next/link\";\nimport { usePathname } from \"next/navigation\";\nimport { cn } from \"@/lib/utils\";\nimport {\n  BarChart3,\n  Search,\n  Globe,\n  Users,\n  Settings,\n  CreditCard,\n  FileText,\n  Zap,\n  Target,\n  Bot,\n  ChevronDown,\n  ChevronRight,\n  Home,\n  Activity,\n  Shield,\n  LinkIcon,\n  Smartphone,\n  Code,\n  Eye,\n  TrendingUp,\n  Bell,\n  Puzzle,\n} from \"@/components/ui/icons\";\n\ninterface NavItem {\n  title: string;\n  href?: string;\n  icon: React.ComponentType<{ className?: string }>;\n  children?: NavItem[];\n  badge?: string;\n}\n\nconst navigation: NavItem[] = [\n  {\n    title: \"Dashboard\",\n    href: \"/dashboard\",\n    icon: Home,\n  },\n  {\n    title: \"Core Audit\",\n    icon: Activity,\n    children: [\n      {\n        title: \"PageSpeed Performance\",\n        href: \"/audit/pagespeed\",\n        icon: TrendingUp,\n      },\n      { title: \"Core Web Vitals\", href: \"/audit/web-vitals\", icon: Activity },\n      {\n        title: \"Mobile Responsiveness\",\n        href: \"/audit/mobile\",\n        icon: Smartphone,\n      },\n      { title: \"HTTPS & SSL Check\", href: \"/audit/ssl\", icon: Shield },\n      {\n        title: \"Broken Link Detection\",\n        href: \"/audit/broken-links\",\n        icon: LinkIcon,\n      },\n      { title: \"Redirect Chain Check\", href: \"/audit/redirects\", icon: Target },\n      { title: \"Minification Checks\", href: \"/audit/minification\", icon: Code },\n    ],\n  },\n  {\n    title: \"SEO Analysis\",\n    icon: Search,\n    children: [\n      { title: \"Meta Tags Audit\", href: \"/seo/meta-tags\", icon: FileText },\n      { title: \"Heading Structure\", href: \"/seo/headings\", icon: FileText },\n      { title: \"Alt Text Coverage\", href: \"/seo/alt-text\", icon: Eye },\n      {\n        title: \"Robots.txt & Sitemap\",\n        href: \"/seo/robots-sitemap\",\n        icon: FileText,\n      },\n      {\n        title: \"Internal Linking\",\n        href: \"/seo/internal-links\",\n        icon: LinkIcon,\n      },\n      { title: \"Structured Data\", href: \"/seo/structured-data\", icon: Code },\n    ],\n  },\n  {\n    title: \"Web Scraping\",\n    icon: Globe,\n    children: [\n      { title: \"Domain Scraping\", href: \"/scraping/domain\", icon: Globe },\n      { title: \"Targeted Scraping\", href: \"/scraping/targeted\", icon: Target },\n      {\n        title: \"Price & Review Extraction\",\n        href: \"/scraping/price-review\",\n        icon: BarChart3,\n      },\n      { title: \"HTML & Metadata\", href: \"/scraping/html-metadata\", icon: Code },\n      {\n        title: \"Scraper Rule Builder\",\n        href: \"/scraping/rule-builder\",\n        icon: Puzzle,\n      },\n    ],\n  },\n  {\n    title: \"Competitor Analysis\",\n    icon: Users,\n    children: [\n      {\n        title: \"Keyword Gap Analysis\",\n        href: \"/competitor/keyword-gap\",\n        icon: Search,\n      },\n      {\n        title: \"Side-by-side SEO\",\n        href: \"/competitor/seo-comparison\",\n        icon: BarChart3,\n      },\n      {\n        title: \"Backlink Analysis\",\n        href: \"/competitor/backlinks\",\n        icon: LinkIcon,\n      },\n      {\n        title: \"Traffic Estimation\",\n        href: \"/competitor/traffic\",\n        icon: TrendingUp,\n      },\n      {\n        title: \"Audit Benchmarking\",\n        href: \"/competitor/benchmarking\",\n        icon: Target,\n      },\n    ],\n  },\n  {\n    title: \"AI Features\",\n    icon: Bot,\n    children: [\n      { title: \"Audit Summary\", href: \"/ai/audit-summary\", icon: FileText },\n      { title: \"Fix Prioritization\", href: \"/ai/fix-priority\", icon: Target },\n      { title: \"Keyword Insights\", href: \"/ai/keyword-insights\", icon: Search },\n      {\n        title: \"Content Analysis\",\n        href: \"/ai/content-analysis\",\n        icon: FileText,\n      },\n      { title: \"Ask AI Anything\", href: \"/ai/chat\", icon: Bot },\n      { title: \"Blog Idea Generator\", href: \"/ai/blog-ideas\", icon: Zap },\n      { title: \"Meta Tag Generator\", href: \"/ai/meta-generator\", icon: Code },\n    ],\n  },\n  {\n    title: \"Reports\",\n    icon: FileText,\n    children: [\n      { title: \"PDF Reports\", href: \"/reports/pdf\", icon: FileText },\n      { title: \"Email Reports\", href: \"/reports/email\", icon: Bell },\n      { title: \"Shareable Links\", href: \"/reports/shareable\", icon: LinkIcon },\n      { title: \"Scheduled Reports\", href: \"/reports/scheduled\", icon: Bell },\n    ],\n  },\n  {\n    title: \"Organization\",\n    href: \"/organization\",\n    icon: Users,\n  },\n  {\n    title: \"Subscription\",\n    href: \"/subscription\",\n    icon: CreditCard,\n  },\n  {\n    title: \"Integrations\",\n    href: \"/integrations\",\n    icon: Puzzle,\n  },\n  {\n    title: \"Settings\",\n    href: \"/settings\",\n    icon: Settings,\n  },\n];\n\ninterface SidebarProps {\n  className?: string;\n}\n\nexport function Sidebar({ className }: SidebarProps) {\n  const pathname = usePathname();\n  const [expandedItems, setExpandedItems] = useState<string[]>([]);\n\n  const toggleExpanded = (title: string) => {\n    setExpandedItems((prev) =>\n      prev.includes(title)\n        ? prev.filter((item) => item !== title)\n        : [...prev, title]\n    );\n  };\n\n  const renderNavItem = (item: NavItem, level = 0) => {\n    const isExpanded = expandedItems.includes(item.title);\n    const hasChildren = item.children && item.children.length > 0;\n    const isActive = item.href ? pathname === item.href : false;\n\n    return (\n      <div key={item.title}>\n        {item.href ? (\n          <Link\n            href={item.href}\n            className={cn(\n              \"flex items-center gap-3 rounded-lg px-3 py-2 text-sm font-medium transition-colors\",\n              \"hover:bg-accent hover:text-accent-foreground\",\n              isActive && \"bg-accent text-accent-foreground\",\n              level > 0 && \"ml-6\"\n            )}\n          >\n            <item.icon className=\"h-4 w-4\" />\n            {item.title}\n            {item.badge && (\n              <span className=\"ml-auto rounded-full bg-primary px-2 py-1 text-xs text-primary-foreground\">\n                {item.badge}\n              </span>\n            )}\n          </Link>\n        ) : (\n          <button\n            onClick={() => hasChildren && toggleExpanded(item.title)}\n            className={cn(\n              \"flex w-full items-center gap-3 rounded-lg px-3 py-2 text-sm font-medium transition-colors\",\n              \"hover:bg-accent hover:text-accent-foreground\",\n              level > 0 && \"ml-6\"\n            )}\n          >\n            <item.icon className=\"h-4 w-4\" />\n            {item.title}\n            {item.badge && (\n              <span className=\"ml-auto rounded-full bg-primary px-2 py-1 text-xs text-primary-foreground\">\n                {item.badge}\n              </span>\n            )}\n            {hasChildren && (\n              <div className=\"ml-auto\">\n                {isExpanded ? (\n                  <ChevronDown className=\"h-4 w-4\" />\n                ) : (\n                  <ChevronRight className=\"h-4 w-4\" />\n                )}\n              </div>\n            )}\n          </button>\n        )}\n        {hasChildren && isExpanded && (\n          <div className=\"mt-1 space-y-1\">\n            {item.children?.map((child) => renderNavItem(child, level + 1))}\n          </div>\n        )}\n      </div>\n    );\n  };\n\n  return (\n    <div\n      className={cn(\"flex h-full w-64 flex-col border-r bg-card\", className)}\n    >\n      <div className=\"flex h-16 items-center border-b px-6\">\n        <Link\n          href=\"/dashboard\"\n          className=\"flex items-center gap-2 font-semibold\"\n        >\n          <Bot className=\"h-6 w-6 text-primary\" />\n          <span>WebAnalyzer Pro</span>\n        </Link>\n      </div>\n      <nav className=\"flex-1 space-y-1 p-4 overflow-y-auto\">\n        {navigation.map((item) => renderNavItem(item))}\n      </nav>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAuCA,MAAM,aAAwB;IAC5B;QACE,OAAO;QACP,MAAM;QACN,MAAM,iIAAA,CAAA,OAAI;IACZ;IACA;QACE,OAAO;QACP,MAAM,iIAAA,CAAA,WAAQ;QACd,UAAU;YACR;gBACE,OAAO;gBACP,MAAM;gBACN,MAAM,iIAAA,CAAA,aAAU;YAClB;YACA;gBAAE,OAAO;gBAAmB,MAAM;gBAAqB,MAAM,iIAAA,CAAA,WAAQ;YAAC;YACtE;gBACE,OAAO;gBACP,MAAM;gBACN,MAAM,iIAAA,CAAA,aAAU;YAClB;YACA;gBAAE,OAAO;gBAAqB,MAAM;gBAAc,MAAM,iIAAA,CAAA,SAAM;YAAC;YAC/D;gBACE,OAAO;gBACP,MAAM;gBACN,MAAM,iIAAA,CAAA,WAAQ;YAChB;YACA;gBAAE,OAAO;gBAAwB,MAAM;gBAAoB,MAAM,iIAAA,CAAA,SAAM;YAAC;YACxE;gBAAE,OAAO;gBAAuB,MAAM;gBAAuB,MAAM,iIAAA,CAAA,OAAI;YAAC;SACzE;IACH;IACA;QACE,OAAO;QACP,MAAM,iIAAA,CAAA,SAAM;QACZ,UAAU;YACR;gBAAE,OAAO;gBAAmB,MAAM;gBAAkB,MAAM,iIAAA,CAAA,WAAQ;YAAC;YACnE;gBAAE,OAAO;gBAAqB,MAAM;gBAAiB,MAAM,iIAAA,CAAA,WAAQ;YAAC;YACpE;gBAAE,OAAO;gBAAqB,MAAM;gBAAiB,MAAM,iIAAA,CAAA,MAAG;YAAC;YAC/D;gBACE,OAAO;gBACP,MAAM;gBACN,MAAM,iIAAA,CAAA,WAAQ;YAChB;YACA;gBACE,OAAO;gBACP,MAAM;gBACN,MAAM,iIAAA,CAAA,WAAQ;YAChB;YACA;gBAAE,OAAO;gBAAmB,MAAM;gBAAwB,MAAM,iIAAA,CAAA,OAAI;YAAC;SACtE;IACH;IACA;QACE,OAAO;QACP,MAAM,iIAAA,CAAA,QAAK;QACX,UAAU;YACR;gBAAE,OAAO;gBAAmB,MAAM;gBAAoB,MAAM,iIAAA,CAAA,QAAK;YAAC;YAClE;gBAAE,OAAO;gBAAqB,MAAM;gBAAsB,MAAM,iIAAA,CAAA,SAAM;YAAC;YACvE;gBACE,OAAO;gBACP,MAAM;gBACN,MAAM,iIAAA,CAAA,YAAS;YACjB;YACA;gBAAE,OAAO;gBAAmB,MAAM;gBAA2B,MAAM,iIAAA,CAAA,OAAI;YAAC;YACxE;gBACE,OAAO;gBACP,MAAM;gBACN,MAAM,iIAAA,CAAA,SAAM;YACd;SACD;IACH;IACA;QACE,OAAO;QACP,MAAM,iIAAA,CAAA,QAAK;QACX,UAAU;YACR;gBACE,OAAO;gBACP,MAAM;gBACN,MAAM,iIAAA,CAAA,SAAM;YACd;YACA;gBACE,OAAO;gBACP,MAAM;gBACN,MAAM,iIAAA,CAAA,YAAS;YACjB;YACA;gBACE,OAAO;gBACP,MAAM;gBACN,MAAM,iIAAA,CAAA,WAAQ;YAChB;YACA;gBACE,OAAO;gBACP,MAAM;gBACN,MAAM,iIAAA,CAAA,aAAU;YAClB;YACA;gBACE,OAAO;gBACP,MAAM;gBACN,MAAM,iIAAA,CAAA,SAAM;YACd;SACD;IACH;IACA;QACE,OAAO;QACP,MAAM,iIAAA,CAAA,MAAG;QACT,UAAU;YACR;gBAAE,OAAO;gBAAiB,MAAM;gBAAqB,MAAM,iIAAA,CAAA,WAAQ;YAAC;YACpE;gBAAE,OAAO;gBAAsB,MAAM;gBAAoB,MAAM,iIAAA,CAAA,SAAM;YAAC;YACtE;gBAAE,OAAO;gBAAoB,MAAM;gBAAwB,MAAM,iIAAA,CAAA,SAAM;YAAC;YACxE;gBACE,OAAO;gBACP,MAAM;gBACN,MAAM,iIAAA,CAAA,WAAQ;YAChB;YACA;gBAAE,OAAO;gBAAmB,MAAM;gBAAY,MAAM,iIAAA,CAAA,MAAG;YAAC;YACxD;gBAAE,OAAO;gBAAuB,MAAM;gBAAkB,MAAM,iIAAA,CAAA,MAAG;YAAC;YAClE;gBAAE,OAAO;gBAAsB,MAAM;gBAAsB,MAAM,iIAAA,CAAA,OAAI;YAAC;SACvE;IACH;IACA;QACE,OAAO;QACP,MAAM,iIAAA,CAAA,WAAQ;QACd,UAAU;YACR;gBAAE,OAAO;gBAAe,MAAM;gBAAgB,MAAM,iIAAA,CAAA,WAAQ;YAAC;YAC7D;gBAAE,OAAO;gBAAiB,MAAM;gBAAkB,MAAM,iIAAA,CAAA,OAAI;YAAC;YAC7D;gBAAE,OAAO;gBAAmB,MAAM;gBAAsB,MAAM,iIAAA,CAAA,WAAQ;YAAC;YACvE;gBAAE,OAAO;gBAAqB,MAAM;gBAAsB,MAAM,iIAAA,CAAA,OAAI;YAAC;SACtE;IACH;IACA;QACE,OAAO;QACP,MAAM;QACN,MAAM,iIAAA,CAAA,QAAK;IACb;IACA;QACE,OAAO;QACP,MAAM;QACN,MAAM,iIAAA,CAAA,aAAU;IAClB;IACA;QACE,OAAO;QACP,MAAM;QACN,MAAM,iIAAA,CAAA,SAAM;IACd;IACA;QACE,OAAO;QACP,MAAM;QACN,MAAM,iIAAA,CAAA,WAAQ;IAChB;CACD;AAMM,SAAS,QAAQ,EAAE,SAAS,EAAgB;IACjD,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAE/D,MAAM,iBAAiB,CAAC;QACtB,iBAAiB,CAAC,OAChB,KAAK,QAAQ,CAAC,SACV,KAAK,MAAM,CAAC,CAAC,OAAS,SAAS,SAC/B;mBAAI;gBAAM;aAAM;IAExB;IAEA,MAAM,gBAAgB,CAAC,MAAe,QAAQ,CAAC;QAC7C,MAAM,aAAa,cAAc,QAAQ,CAAC,KAAK,KAAK;QACpD,MAAM,cAAc,KAAK,QAAQ,IAAI,KAAK,QAAQ,CAAC,MAAM,GAAG;QAC5D,MAAM,WAAW,KAAK,IAAI,GAAG,aAAa,KAAK,IAAI,GAAG;QAEtD,qBACE,8OAAC;;gBACE,KAAK,IAAI,iBACR,8OAAC,4JAAA,CAAA,UAAI;oBACH,MAAM,KAAK,IAAI;oBACf,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sFACA,gDACA,YAAY,oCACZ,QAAQ,KAAK;;sCAGf,8OAAC,KAAK,IAAI;4BAAC,WAAU;;;;;;wBACpB,KAAK,KAAK;wBACV,KAAK,KAAK,kBACT,8OAAC;4BAAK,WAAU;sCACb,KAAK,KAAK;;;;;;;;;;;yCAKjB,8OAAC;oBACC,SAAS,IAAM,eAAe,eAAe,KAAK,KAAK;oBACvD,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6FACA,gDACA,QAAQ,KAAK;;sCAGf,8OAAC,KAAK,IAAI;4BAAC,WAAU;;;;;;wBACpB,KAAK,KAAK;wBACV,KAAK,KAAK,kBACT,8OAAC;4BAAK,WAAU;sCACb,KAAK,KAAK;;;;;;wBAGd,6BACC,8OAAC;4BAAI,WAAU;sCACZ,2BACC,8OAAC,iIAAA,CAAA,cAAW;gCAAC,WAAU;;;;;qDAEvB,8OAAC,iIAAA,CAAA,eAAY;gCAAC,WAAU;;;;;;;;;;;;;;;;;gBAMjC,eAAe,4BACd,8OAAC;oBAAI,WAAU;8BACZ,KAAK,QAAQ,EAAE,IAAI,CAAC,QAAU,cAAc,OAAO,QAAQ;;;;;;;WAhDxD,KAAK,KAAK;;;;;IAqDxB;IAEA,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8CAA8C;;0BAE5D,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;oBACH,MAAK;oBACL,WAAU;;sCAEV,8OAAC,iIAAA,CAAA,MAAG;4BAAC,WAAU;;;;;;sCACf,8OAAC;sCAAK;;;;;;;;;;;;;;;;;0BAGV,8OAAC;gBAAI,WAAU;0BACZ,WAAW,GAAG,CAAC,CAAC,OAAS,cAAc;;;;;;;;;;;;AAIhD", "debugId": null}}, {"offset": {"line": 1697, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/UI/web-analyzer-saas/src/components/layout/header.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\nimport Link from \"next/link\";\nimport { cn } from \"@/lib/utils\";\nimport {\n  Bell,\n  Search,\n  Settings,\n  User,\n  LogOut,\n  CreditCard,\n  HelpCircle,\n  Menu,\n  X,\n  ChevronDown,\n} from \"@/components/ui/icons\";\n\ninterface HeaderProps {\n  onMenuClick?: () => void;\n  className?: string;\n}\n\nexport function Header({ onMenuClick, className }: HeaderProps) {\n  const [isProfileOpen, setIsProfileOpen] = useState(false);\n  const [isNotificationsOpen, setIsNotificationsOpen] = useState(false);\n\n  // Mock user data - replace with actual user context\n  const user = {\n    name: \"<PERSON>\",\n    email: \"<EMAIL>\",\n    avatar: \"/avatars/john-doe.jpg\",\n    organization: \"Acme Corp\",\n    plan: \"Pro\",\n  };\n\n  // Mock notifications - replace with actual notifications\n  const notifications = [\n    {\n      id: 1,\n      title: \"Audit Complete\",\n      message: \"Website audit for example.com has finished\",\n      time: \"2 minutes ago\",\n      unread: true,\n    },\n    {\n      id: 2,\n      title: \"New Competitor Found\",\n      message: \"We found a new competitor in your industry\",\n      time: \"1 hour ago\",\n      unread: true,\n    },\n    {\n      id: 3,\n      title: \"Monthly Report Ready\",\n      message: \"Your monthly SEO report is ready for download\",\n      time: \"2 hours ago\",\n      unread: false,\n    },\n  ];\n\n  const unreadCount = notifications.filter((n) => n.unread).length;\n\n  return (\n    <header\n      className={cn(\n        \"flex h-16 items-center justify-between border-b bg-background px-6\",\n        className\n      )}\n    >\n      {/* Left side - Mobile menu button and search */}\n      <div className=\"flex items-center gap-4\">\n        <button\n          onClick={onMenuClick}\n          className=\"lg:hidden rounded-md p-2 hover:bg-accent\"\n        >\n          <Menu className=\"h-5 w-5\" />\n        </button>\n\n        <div className=\"relative hidden md:block\">\n          <Search className=\"absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground\" />\n          <input\n            type=\"search\"\n            placeholder=\"Search websites, audits, reports...\"\n            className=\"w-64 rounded-md border border-input bg-background pl-10 pr-4 py-2 text-sm placeholder:text-muted-foreground focus:border-ring focus:outline-none focus:ring-1 focus:ring-ring\"\n          />\n        </div>\n      </div>\n\n      {/* Right side - Notifications, user menu */}\n      <div className=\"flex items-center gap-4\">\n        {/* Quick Actions */}\n        <div className=\"hidden md:flex items-center gap-2\">\n          <Link\n            href=\"/audit/new\"\n            className=\"rounded-md bg-primary px-4 py-2 text-sm font-medium text-primary-foreground hover:bg-primary/90\"\n          >\n            New Audit\n          </Link>\n        </div>\n\n        {/* Notifications */}\n        <div className=\"relative\">\n          <button\n            onClick={() => setIsNotificationsOpen(!isNotificationsOpen)}\n            className=\"relative rounded-md p-2 hover:bg-accent\"\n          >\n            <Bell className=\"h-5 w-5\" />\n            {unreadCount > 0 && (\n              <span className=\"absolute -top-1 -right-1 flex h-5 w-5 items-center justify-center rounded-full bg-destructive text-xs text-destructive-foreground\">\n                {unreadCount}\n              </span>\n            )}\n          </button>\n\n          {isNotificationsOpen && (\n            <div className=\"absolute right-0 top-full mt-2 w-80 rounded-md border bg-popover p-4 shadow-lg z-50\">\n              <div className=\"flex items-center justify-between mb-4\">\n                <h3 className=\"font-semibold\">Notifications</h3>\n                <button\n                  onClick={() => setIsNotificationsOpen(false)}\n                  className=\"rounded-md p-1 hover:bg-accent\"\n                >\n                  <X className=\"h-4 w-4\" />\n                </button>\n              </div>\n              <div className=\"space-y-3\">\n                {notifications.map((notification) => (\n                  <div\n                    key={notification.id}\n                    className={cn(\n                      \"rounded-md p-3 text-sm\",\n                      notification.unread ? \"bg-accent\" : \"bg-muted/50\"\n                    )}\n                  >\n                    <div className=\"font-medium\">{notification.title}</div>\n                    <div className=\"text-muted-foreground\">\n                      {notification.message}\n                    </div>\n                    <div className=\"text-xs text-muted-foreground mt-1\">\n                      {notification.time}\n                    </div>\n                  </div>\n                ))}\n              </div>\n              <div className=\"mt-4 pt-4 border-t\">\n                <Link\n                  href=\"/notifications\"\n                  className=\"text-sm text-primary hover:underline\"\n                >\n                  View all notifications\n                </Link>\n              </div>\n            </div>\n          )}\n        </div>\n\n        {/* User Menu */}\n        <div className=\"relative\">\n          <button\n            onClick={() => setIsProfileOpen(!isProfileOpen)}\n            className=\"flex items-center gap-2 rounded-md p-2 hover:bg-accent\"\n          >\n            <div className=\"flex h-8 w-8 items-center justify-center rounded-full bg-primary text-primary-foreground text-sm font-medium\">\n              {user.name\n                .split(\" \")\n                .map((n) => n[0])\n                .join(\"\")}\n            </div>\n            <div className=\"hidden md:block text-left\">\n              <div className=\"text-sm font-medium\">{user.name}</div>\n              <div className=\"text-xs text-muted-foreground\">\n                {user.organization}\n              </div>\n            </div>\n            <ChevronDown className=\"h-4 w-4 text-muted-foreground\" />\n          </button>\n\n          {isProfileOpen && (\n            <div className=\"absolute right-0 top-full mt-2 w-56 rounded-md border bg-popover p-2 shadow-lg z-50\">\n              <div className=\"px-3 py-2 border-b\">\n                <div className=\"font-medium\">{user.name}</div>\n                <div className=\"text-sm text-muted-foreground\">\n                  {user.email}\n                </div>\n                <div className=\"text-xs text-muted-foreground mt-1\">\n                  {user.plan} Plan • {user.organization}\n                </div>\n              </div>\n\n              <div className=\"py-2\">\n                <Link\n                  href=\"/profile\"\n                  className=\"flex items-center gap-2 rounded-md px-3 py-2 text-sm hover:bg-accent\"\n                >\n                  <User className=\"h-4 w-4\" />\n                  Profile\n                </Link>\n                <Link\n                  href=\"/organization\"\n                  className=\"flex items-center gap-2 rounded-md px-3 py-2 text-sm hover:bg-accent\"\n                >\n                  <Settings className=\"h-4 w-4\" />\n                  Organization\n                </Link>\n                <Link\n                  href=\"/subscription\"\n                  className=\"flex items-center gap-2 rounded-md px-3 py-2 text-sm hover:bg-accent\"\n                >\n                  <CreditCard className=\"h-4 w-4\" />\n                  Billing\n                </Link>\n                <Link\n                  href=\"/help\"\n                  className=\"flex items-center gap-2 rounded-md px-3 py-2 text-sm hover:bg-accent\"\n                >\n                  <HelpCircle className=\"h-4 w-4\" />\n                  Help & Support\n                </Link>\n              </div>\n\n              <div className=\"border-t pt-2\">\n                <button className=\"flex w-full items-center gap-2 rounded-md px-3 py-2 text-sm hover:bg-accent text-destructive\">\n                  <LogOut className=\"h-4 w-4\" />\n                  Sign out\n                </button>\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAuBO,SAAS,OAAO,EAAE,WAAW,EAAE,SAAS,EAAe;IAC5D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/D,oDAAoD;IACpD,MAAM,OAAO;QACX,MAAM;QACN,OAAO;QACP,QAAQ;QACR,cAAc;QACd,MAAM;IACR;IAEA,yDAAyD;IACzD,MAAM,gBAAgB;QACpB;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,MAAM;YACN,QAAQ;QACV;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,MAAM;YACN,QAAQ;QACV;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,MAAM;YACN,QAAQ;QACV;KACD;IAED,MAAM,cAAc,cAAc,MAAM,CAAC,CAAC,IAAM,EAAE,MAAM,EAAE,MAAM;IAEhE,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sEACA;;0BAIF,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,SAAS;wBACT,WAAU;kCAEV,cAAA,8OAAC,iIAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;;;;;;kCAGlB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,iIAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,8OAAC;gCACC,MAAK;gCACL,aAAY;gCACZ,WAAU;;;;;;;;;;;;;;;;;;0BAMhB,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;sCACX;;;;;;;;;;;kCAMH,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,SAAS,IAAM,uBAAuB,CAAC;gCACvC,WAAU;;kDAEV,8OAAC,iIAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCACf,cAAc,mBACb,8OAAC;wCAAK,WAAU;kDACb;;;;;;;;;;;;4BAKN,qCACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAgB;;;;;;0DAC9B,8OAAC;gDACC,SAAS,IAAM,uBAAuB;gDACtC,WAAU;0DAEV,cAAA,8OAAC,iIAAA,CAAA,IAAC;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAGjB,8OAAC;wCAAI,WAAU;kDACZ,cAAc,GAAG,CAAC,CAAC,6BAClB,8OAAC;gDAEC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0BACA,aAAa,MAAM,GAAG,cAAc;;kEAGtC,8OAAC;wDAAI,WAAU;kEAAe,aAAa,KAAK;;;;;;kEAChD,8OAAC;wDAAI,WAAU;kEACZ,aAAa,OAAO;;;;;;kEAEvB,8OAAC;wDAAI,WAAU;kEACZ,aAAa,IAAI;;;;;;;+CAXf,aAAa,EAAE;;;;;;;;;;kDAgB1B,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;kCAST,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,SAAS,IAAM,iBAAiB,CAAC;gCACjC,WAAU;;kDAEV,8OAAC;wCAAI,WAAU;kDACZ,KAAK,IAAI,CACP,KAAK,CAAC,KACN,GAAG,CAAC,CAAC,IAAM,CAAC,CAAC,EAAE,EACf,IAAI,CAAC;;;;;;kDAEV,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAAuB,KAAK,IAAI;;;;;;0DAC/C,8OAAC;gDAAI,WAAU;0DACZ,KAAK,YAAY;;;;;;;;;;;;kDAGtB,8OAAC,iIAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;;;;;;;4BAGxB,+BACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAAe,KAAK,IAAI;;;;;;0DACvC,8OAAC;gDAAI,WAAU;0DACZ,KAAK,KAAK;;;;;;0DAEb,8OAAC;gDAAI,WAAU;;oDACZ,KAAK,IAAI;oDAAC;oDAAS,KAAK,YAAY;;;;;;;;;;;;;kDAIzC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;;kEAEV,8OAAC,iIAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAY;;;;;;;0DAG9B,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;;kEAEV,8OAAC,iIAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAY;;;;;;;0DAGlC,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;;kEAEV,8OAAC,iIAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;oDAAY;;;;;;;0DAGpC,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;;kEAEV,8OAAC,iIAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;oDAAY;;;;;;;;;;;;;kDAKtC,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAO,WAAU;;8DAChB,8OAAC,iIAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;gDAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUhD", "debugId": null}}, {"offset": {"line": 2178, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/UI/web-analyzer-saas/src/components/layout/app-layout.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from \"react\"\nimport { cn } from \"@/lib/utils\"\nimport { Sidebar } from \"./sidebar\"\nimport { Header } from \"./header\"\n\ninterface AppLayoutProps {\n  children: React.ReactNode\n  className?: string\n}\n\nexport function AppLayout({ children, className }: AppLayoutProps) {\n  const [sidebarOpen, setSidebarOpen] = useState(false)\n\n  return (\n    <div className=\"flex h-screen bg-background\">\n      {/* Desktop Sidebar */}\n      <div className=\"hidden lg:block\">\n        <Sidebar />\n      </div>\n\n      {/* Mobile Sidebar Overlay */}\n      {sidebarOpen && (\n        <div className=\"fixed inset-0 z-50 lg:hidden\">\n          <div \n            className=\"absolute inset-0 bg-black/50\" \n            onClick={() => setSidebarOpen(false)}\n          />\n          <div className=\"absolute left-0 top-0 h-full\">\n            <Sidebar />\n          </div>\n        </div>\n      )}\n\n      {/* Main Content */}\n      <div className=\"flex flex-1 flex-col overflow-hidden\">\n        <Header onMenuClick={() => setSidebarOpen(true)} />\n        <main className={cn(\"flex-1 overflow-y-auto p-6\", className)}>\n          {children}\n        </main>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAYO,SAAS,UAAU,EAAE,QAAQ,EAAE,SAAS,EAAkB;IAC/D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,uIAAA,CAAA,UAAO;;;;;;;;;;YAIT,6BACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,WAAU;wBACV,SAAS,IAAM,eAAe;;;;;;kCAEhC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,uIAAA,CAAA,UAAO;;;;;;;;;;;;;;;;0BAMd,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,sIAAA,CAAA,SAAM;wBAAC,aAAa,IAAM,eAAe;;;;;;kCAC1C,8OAAC;wBAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;kCAC/C;;;;;;;;;;;;;;;;;;AAKX", "debugId": null}}]}