"use client"

import { useState } from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { cn } from "@/lib/utils"
import {
  BarChart3,
  Search,
  Globe,
  Users,
  Settings,
  CreditCard,
  FileText,
  Zap,
  Target,
  Bot,
  ChevronDown,
  ChevronRight,
  Home,
  Activity,
  Shield,
  Link as LinkIcon,
  Smartphone,
  Code,
  Eye,
  TrendingUp,
  UserCheck,
  Database,
  Puzzle,
  Bell,
  HelpCircle,
} from "lucide-react"

interface NavItem {
  title: string
  href?: string
  icon: React.ComponentType<{ className?: string }>
  children?: NavItem[]
  badge?: string
}

const navigation: NavItem[] = [
  {
    title: "Dashboard",
    href: "/dashboard",
    icon: Home,
  },
  {
    title: "Core Audit",
    icon: Activity,
    children: [
      { title: "PageSpeed Performance", href: "/audit/pagespeed", icon: TrendingUp },
      { title: "Core Web Vitals", href: "/audit/web-vitals", icon: Activity },
      { title: "Mobile Responsiveness", href: "/audit/mobile", icon: Smartphone },
      { title: "HTTPS & SSL Check", href: "/audit/ssl", icon: Shield },
      { title: "Broken Link Detection", href: "/audit/broken-links", icon: LinkIcon },
      { title: "Redirect Chain Check", href: "/audit/redirects", icon: Target },
      { title: "Minification Checks", href: "/audit/minification", icon: Code },
    ],
  },
  {
    title: "SEO Analysis",
    icon: Search,
    children: [
      { title: "Meta Tags Audit", href: "/seo/meta-tags", icon: FileText },
      { title: "Heading Structure", href: "/seo/headings", icon: FileText },
      { title: "Alt Text Coverage", href: "/seo/alt-text", icon: Eye },
      { title: "Robots.txt & Sitemap", href: "/seo/robots-sitemap", icon: FileText },
      { title: "Internal Linking", href: "/seo/internal-links", icon: LinkIcon },
      { title: "Structured Data", href: "/seo/structured-data", icon: Code },
    ],
  },
  {
    title: "Web Scraping",
    icon: Globe,
    children: [
      { title: "Domain Scraping", href: "/scraping/domain", icon: Globe },
      { title: "Targeted Scraping", href: "/scraping/targeted", icon: Target },
      { title: "Price & Review Extraction", href: "/scraping/price-review", icon: BarChart3 },
      { title: "HTML & Metadata", href: "/scraping/html-metadata", icon: Code },
      { title: "Scraper Rule Builder", href: "/scraping/rule-builder", icon: Puzzle },
    ],
  },
  {
    title: "Competitor Analysis",
    icon: Users,
    children: [
      { title: "Keyword Gap Analysis", href: "/competitor/keyword-gap", icon: Search },
      { title: "Side-by-side SEO", href: "/competitor/seo-comparison", icon: BarChart3 },
      { title: "Backlink Analysis", href: "/competitor/backlinks", icon: LinkIcon },
      { title: "Traffic Estimation", href: "/competitor/traffic", icon: TrendingUp },
      { title: "Audit Benchmarking", href: "/competitor/benchmarking", icon: Target },
    ],
  },
  {
    title: "AI Features",
    icon: Bot,
    children: [
      { title: "Audit Summary", href: "/ai/audit-summary", icon: FileText },
      { title: "Fix Prioritization", href: "/ai/fix-priority", icon: Target },
      { title: "Keyword Insights", href: "/ai/keyword-insights", icon: Search },
      { title: "Content Analysis", href: "/ai/content-analysis", icon: FileText },
      { title: "Ask AI Anything", href: "/ai/chat", icon: Bot },
      { title: "Blog Idea Generator", href: "/ai/blog-ideas", icon: Zap },
      { title: "Meta Tag Generator", href: "/ai/meta-generator", icon: Code },
    ],
  },
  {
    title: "Reports",
    icon: FileText,
    children: [
      { title: "PDF Reports", href: "/reports/pdf", icon: FileText },
      { title: "Email Reports", href: "/reports/email", icon: Bell },
      { title: "Shareable Links", href: "/reports/shareable", icon: LinkIcon },
      { title: "Scheduled Reports", href: "/reports/scheduled", icon: Bell },
    ],
  },
  {
    title: "Organization",
    href: "/organization",
    icon: Users,
  },
  {
    title: "Subscription",
    href: "/subscription",
    icon: CreditCard,
  },
  {
    title: "Integrations",
    href: "/integrations",
    icon: Puzzle,
  },
  {
    title: "Settings",
    href: "/settings",
    icon: Settings,
  },
]

interface SidebarProps {
  className?: string
}

export function Sidebar({ className }: SidebarProps) {
  const pathname = usePathname()
  const [expandedItems, setExpandedItems] = useState<string[]>([])

  const toggleExpanded = (title: string) => {
    setExpandedItems(prev =>
      prev.includes(title)
        ? prev.filter(item => item !== title)
        : [...prev, title]
    )
  }

  const renderNavItem = (item: NavItem, level = 0) => {
    const isExpanded = expandedItems.includes(item.title)
    const hasChildren = item.children && item.children.length > 0
    const isActive = item.href ? pathname === item.href : false

    return (
      <div key={item.title}>
        {item.href ? (
          <Link
            href={item.href}
            className={cn(
              "flex items-center gap-3 rounded-lg px-3 py-2 text-sm font-medium transition-colors",
              "hover:bg-accent hover:text-accent-foreground",
              isActive && "bg-accent text-accent-foreground",
              level > 0 && "ml-6"
            )}
          >
            <item.icon className="h-4 w-4" />
            {item.title}
            {item.badge && (
              <span className="ml-auto rounded-full bg-primary px-2 py-1 text-xs text-primary-foreground">
                {item.badge}
              </span>
            )}
          </Link>
        ) : (
          <button
            onClick={() => hasChildren && toggleExpanded(item.title)}
            className={cn(
              "flex w-full items-center gap-3 rounded-lg px-3 py-2 text-sm font-medium transition-colors",
              "hover:bg-accent hover:text-accent-foreground",
              level > 0 && "ml-6"
            )}
          >
            <item.icon className="h-4 w-4" />
            {item.title}
            {item.badge && (
              <span className="ml-auto rounded-full bg-primary px-2 py-1 text-xs text-primary-foreground">
                {item.badge}
              </span>
            )}
            {hasChildren && (
              <div className="ml-auto">
                {isExpanded ? (
                  <ChevronDown className="h-4 w-4" />
                ) : (
                  <ChevronRight className="h-4 w-4" />
                )}
              </div>
            )}
          </button>
        )}
        {hasChildren && isExpanded && (
          <div className="mt-1 space-y-1">
            {item.children?.map(child => renderNavItem(child, level + 1))}
          </div>
        )}
      </div>
    )
  }

  return (
    <div className={cn("flex h-full w-64 flex-col border-r bg-card", className)}>
      <div className="flex h-16 items-center border-b px-6">
        <Link href="/dashboard" className="flex items-center gap-2 font-semibold">
          <Bot className="h-6 w-6 text-primary" />
          <span>WebAnalyzer Pro</span>
        </Link>
      </div>
      <nav className="flex-1 space-y-1 p-4 overflow-y-auto">
        {navigation.map(item => renderNavItem(item))}
      </nav>
    </div>
  )
}
