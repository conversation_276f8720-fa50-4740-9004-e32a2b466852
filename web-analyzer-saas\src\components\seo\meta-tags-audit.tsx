"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { 
  Search,
  CheckCircle,
  XCircle,
  AlertTriangle,
  FileText,
  Eye,
  Globe
} from "@/components/ui/icons"

interface MetaTag {
  name: string
  content: string
  status: "good" | "warning" | "error"
  recommendation?: string
}

interface PageMeta {
  url: string
  title: {
    content: string
    length: number
    status: "good" | "warning" | "error"
    issues: string[]
  }
  description: {
    content: string
    length: number
    status: "good" | "warning" | "error"
    issues: string[]
  }
  canonical: {
    url: string
    status: "good" | "warning" | "error"
    issues: string[]
  }
  openGraph: MetaTag[]
  twitterCard: MetaTag[]
  otherMeta: MetaTag[]
}

interface MetaAuditResult {
  url: string
  pages: PageMeta[]
  overallScore: number
  issues: Array<{
    type: "error" | "warning" | "info"
    page: string
    element: string
    message: string
    recommendation: string
  }>
  recommendations: Array<{
    title: string
    description: string
    priority: "high" | "medium" | "low"
    category: string
  }>
}

const mockResults: MetaAuditResult = {
  url: "https://example.com",
  pages: [
    {
      url: "https://example.com/",
      title: {
        content: "Example Website - Best Products Online",
        length: 38,
        status: "good",
        issues: []
      },
      description: {
        content: "Discover amazing products at Example.com. Shop now for the best deals on electronics, clothing, and more. Free shipping on orders over $50.",
        length: 142,
        status: "good",
        issues: []
      },
      canonical: {
        url: "https://example.com/",
        status: "good",
        issues: []
      },
      openGraph: [
        { name: "og:title", content: "Example Website - Best Products Online", status: "good" },
        { name: "og:description", content: "Discover amazing products at Example.com", status: "good" },
        { name: "og:image", content: "https://example.com/og-image.jpg", status: "good" },
        { name: "og:url", content: "https://example.com/", status: "good" }
      ],
      twitterCard: [
        { name: "twitter:card", content: "summary_large_image", status: "good" },
        { name: "twitter:title", content: "Example Website", status: "good" },
        { name: "twitter:description", content: "Discover amazing products", status: "good" }
      ],
      otherMeta: [
        { name: "robots", content: "index, follow", status: "good" },
        { name: "viewport", content: "width=device-width, initial-scale=1", status: "good" }
      ]
    },
    {
      url: "https://example.com/products",
      title: {
        content: "Products",
        length: 8,
        status: "error",
        issues: ["Title too short", "Not descriptive enough"]
      },
      description: {
        content: "",
        length: 0,
        status: "error",
        issues: ["Missing meta description"]
      },
      canonical: {
        url: "",
        status: "warning",
        issues: ["Missing canonical tag"]
      },
      openGraph: [],
      twitterCard: [],
      otherMeta: []
    }
  ],
  overallScore: 72,
  issues: [
    {
      type: "error",
      page: "/products",
      element: "title",
      message: "Title tag is too short (8 characters)",
      recommendation: "Use descriptive titles between 30-60 characters"
    },
    {
      type: "error", 
      page: "/products",
      element: "meta description",
      message: "Missing meta description",
      recommendation: "Add a compelling meta description between 120-160 characters"
    },
    {
      type: "warning",
      page: "/products", 
      element: "canonical",
      message: "Missing canonical URL",
      recommendation: "Add canonical tag to prevent duplicate content issues"
    }
  ],
  recommendations: [
    {
      title: "Optimize title tags",
      description: "Ensure all title tags are descriptive, unique, and between 30-60 characters",
      priority: "high",
      category: "Title Tags"
    },
    {
      title: "Add missing meta descriptions",
      description: "Create compelling meta descriptions for all pages to improve click-through rates",
      priority: "high", 
      category: "Meta Descriptions"
    },
    {
      title: "Implement Open Graph tags",
      description: "Add Open Graph meta tags to improve social media sharing appearance",
      priority: "medium",
      category: "Social Media"
    }
  ]
}

export function MetaTagsAudit() {
  const [url, setUrl] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [results, setResults] = useState<MetaAuditResult | null>(null)

  const handleAudit = async () => {
    if (!url) return
    
    setIsLoading(true)
    setTimeout(() => {
      setResults({ ...mockResults, url })
      setIsLoading(false)
    }, 3000)
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "good": return <CheckCircle className="h-4 w-4 text-green-600" />
      case "warning": return <AlertTriangle className="h-4 w-4 text-yellow-600" />
      case "error": return <XCircle className="h-4 w-4 text-red-600" />
      default: return <AlertTriangle className="h-4 w-4 text-gray-600" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "good": return "text-green-600"
      case "warning": return "text-yellow-600"
      case "error": return "text-red-600"
      default: return "text-gray-600"
    }
  }

  return (
    <div className="space-y-6">
      {/* URL Input */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Meta Tags Analysis
          </CardTitle>
          <CardDescription>
            Analyze meta tags, title tags, descriptions, and social media tags across your website
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4">
            <input
              type="url"
              placeholder="https://example.com"
              value={url}
              onChange={(e) => setUrl(e.target.value)}
              className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <Button 
              onClick={handleAudit} 
              disabled={!url || isLoading}
              className="min-w-[120px]"
            >
              {isLoading ? "Analyzing..." : "Analyze Meta Tags"}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Loading State */}
      {isLoading && (
        <Card>
          <CardContent className="py-8">
            <div className="text-center space-y-4">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
              <p className="text-gray-600">Analyzing meta tags...</p>
              <p className="text-sm text-gray-500">Crawling pages and extracting meta information</p>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Results */}
      {results && !isLoading && (
        <div className="space-y-6">
          {/* Overall Score */}
          <Card>
            <CardHeader>
              <CardTitle>Meta Tags Score</CardTitle>
              <CardDescription>Overall meta tags optimization score</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <div className={`text-4xl font-bold ${results.overallScore >= 80 ? 'text-green-600' : results.overallScore >= 60 ? 'text-yellow-600' : 'text-red-600'}`}>
                    {results.overallScore}
                  </div>
                  <div>
                    <div className="text-sm text-gray-600">Meta Tags Score</div>
                    <div className="text-xs text-gray-500">{results.pages.length} pages analyzed</div>
                  </div>
                </div>
                <FileText className="h-8 w-8 text-gray-400" />
              </div>
            </CardContent>
          </Card>

          {/* Pages Analysis */}
          <Card>
            <CardHeader>
              <CardTitle>Pages Analysis</CardTitle>
              <CardDescription>Meta tags analysis for each page</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {results.pages.map((page, index) => (
                  <div key={index} className="border rounded-lg p-4">
                    <div className="mb-4">
                      <h3 className="font-medium text-lg">{page.url}</h3>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                      {/* Title */}
                      <div className="space-y-2">
                        <div className="flex items-center gap-2">
                          {getStatusIcon(page.title.status)}
                          <span className="font-medium text-sm">Title Tag</span>
                        </div>
                        <div className="text-sm">
                          <div className="font-mono text-xs bg-gray-100 p-2 rounded">
                            {page.title.content || "Missing title tag"}
                          </div>
                          <div className="text-xs text-gray-600 mt-1">
                            Length: {page.title.length} characters
                          </div>
                          {page.title.issues.length > 0 && (
                            <div className="text-xs text-red-600 mt-1">
                              {page.title.issues.join(", ")}
                            </div>
                          )}
                        </div>
                      </div>

                      {/* Description */}
                      <div className="space-y-2">
                        <div className="flex items-center gap-2">
                          {getStatusIcon(page.description.status)}
                          <span className="font-medium text-sm">Meta Description</span>
                        </div>
                        <div className="text-sm">
                          <div className="font-mono text-xs bg-gray-100 p-2 rounded">
                            {page.description.content || "Missing meta description"}
                          </div>
                          <div className="text-xs text-gray-600 mt-1">
                            Length: {page.description.length} characters
                          </div>
                          {page.description.issues.length > 0 && (
                            <div className="text-xs text-red-600 mt-1">
                              {page.description.issues.join(", ")}
                            </div>
                          )}
                        </div>
                      </div>

                      {/* Canonical */}
                      <div className="space-y-2">
                        <div className="flex items-center gap-2">
                          {getStatusIcon(page.canonical.status)}
                          <span className="font-medium text-sm">Canonical URL</span>
                        </div>
                        <div className="text-sm">
                          <div className="font-mono text-xs bg-gray-100 p-2 rounded">
                            {page.canonical.url || "Missing canonical tag"}
                          </div>
                          {page.canonical.issues.length > 0 && (
                            <div className="text-xs text-red-600 mt-1">
                              {page.canonical.issues.join(", ")}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>

                    {/* Social Media Tags */}
                    {(page.openGraph.length > 0 || page.twitterCard.length > 0) && (
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {/* Open Graph */}
                        {page.openGraph.length > 0 && (
                          <div>
                            <h4 className="font-medium text-sm mb-2">Open Graph Tags</h4>
                            <div className="space-y-1">
                              {page.openGraph.map((tag, i) => (
                                <div key={i} className="flex items-center gap-2 text-xs">
                                  {getStatusIcon(tag.status)}
                                  <span className="font-mono">{tag.name}</span>
                                  <span className="text-gray-600 truncate">{tag.content}</span>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}

                        {/* Twitter Card */}
                        {page.twitterCard.length > 0 && (
                          <div>
                            <h4 className="font-medium text-sm mb-2">Twitter Card Tags</h4>
                            <div className="space-y-1">
                              {page.twitterCard.map((tag, i) => (
                                <div key={i} className="flex items-center gap-2 text-xs">
                                  {getStatusIcon(tag.status)}
                                  <span className="font-mono">{tag.name}</span>
                                  <span className="text-gray-600 truncate">{tag.content}</span>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Issues */}
          <Card>
            <CardHeader>
              <CardTitle>Issues Found</CardTitle>
              <CardDescription>Meta tag issues that need attention</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {results.issues.map((issue, index) => (
                  <div key={index} className="flex items-start gap-3 p-3 border rounded-lg">
                    {issue.type === "error" && <XCircle className="h-5 w-5 text-red-600 mt-0.5" />}
                    {issue.type === "warning" && <AlertTriangle className="h-5 w-5 text-yellow-600 mt-0.5" />}
                    {issue.type === "info" && <CheckCircle className="h-5 w-5 text-blue-600 mt-0.5" />}
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <span className="font-medium">{issue.message}</span>
                        <Badge variant="outline" className="text-xs">{issue.page}</Badge>
                      </div>
                      <div className="text-sm text-gray-600 mb-2">
                        Element: <code className="bg-gray-100 px-1 rounded">{issue.element}</code>
                      </div>
                      <div className="text-sm text-blue-600">{issue.recommendation}</div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Recommendations */}
          <Card>
            <CardHeader>
              <CardTitle>Optimization Recommendations</CardTitle>
              <CardDescription>Suggestions to improve your meta tags</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {results.recommendations.map((rec, index) => (
                  <div key={index} className="border rounded-lg p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <h4 className="font-medium">{rec.title}</h4>
                          <Badge variant="outline" className="text-xs">{rec.category}</Badge>
                        </div>
                        <p className="text-sm text-gray-600 mb-3">{rec.description}</p>
                        <Badge 
                          variant={rec.priority === "high" ? "destructive" : rec.priority === "medium" ? "secondary" : "outline"}
                          className="text-xs"
                        >
                          {rec.priority} priority
                        </Badge>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  )
}
