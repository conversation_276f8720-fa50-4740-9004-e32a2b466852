"use client"

import { useState } from "react"
import { <PERSON>, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { 
  Search,
  CheckCircle,
  XCircle,
  AlertTriangle,
  FileText,
  Eye
} from "@/components/ui/icons"

interface Heading {
  level: number
  text: string
  position: number
  issues: string[]
  status: "good" | "warning" | "error"
}

interface PageHeadings {
  url: string
  headings: Heading[]
  h1Count: number
  missingLevels: number[]
  skippedLevels: number[]
  score: number
}

interface HeadingAuditResult {
  url: string
  pages: PageHeadings[]
  overallScore: number
  issues: Array<{
    type: "error" | "warning" | "info"
    page: string
    heading: string
    message: string
    recommendation: string
  }>
  recommendations: Array<{
    title: string
    description: string
    priority: "high" | "medium" | "low"
    category: string
  }>
}

const mockResults: HeadingAuditResult = {
  url: "https://example.com",
  pages: [
    {
      url: "https://example.com/",
      headings: [
        { level: 1, text: "Welcome to Example.com", position: 1, issues: [], status: "good" },
        { level: 2, text: "Our Products", position: 2, issues: [], status: "good" },
        { level: 3, text: "Electronics", position: 3, issues: [], status: "good" },
        { level: 3, text: "Clothing", position: 4, issues: [], status: "good" },
        { level: 2, text: "About Us", position: 5, issues: [], status: "good" },
        { level: 4, text: "Our Mission", position: 6, issues: ["Skipped H3 level"], status: "warning" }
      ],
      h1Count: 1,
      missingLevels: [],
      skippedLevels: [3],
      score: 85
    },
    {
      url: "https://example.com/products",
      headings: [
        { level: 1, text: "Products", position: 1, issues: [], status: "good" },
        { level: 1, text: "Featured Items", position: 2, issues: ["Multiple H1 tags"], status: "error" },
        { level: 3, text: "Category 1", position: 3, issues: ["Missing H2 level"], status: "error" },
        { level: 4, text: "Subcategory", position: 4, issues: ["Skipped H3 level"], status: "warning" }
      ],
      h1Count: 2,
      missingLevels: [2],
      skippedLevels: [2, 3],
      score: 45
    }
  ],
  overallScore: 65,
  issues: [
    {
      type: "error",
      page: "/products",
      heading: "Featured Items (H1)",
      message: "Multiple H1 tags found on the same page",
      recommendation: "Use only one H1 tag per page for the main heading"
    },
    {
      type: "error",
      page: "/products", 
      heading: "Category 1 (H3)",
      message: "H3 used without H2 parent",
      recommendation: "Ensure proper heading hierarchy by not skipping levels"
    },
    {
      type: "warning",
      page: "/",
      heading: "Our Mission (H4)",
      message: "H4 used without H3 parent",
      recommendation: "Use H3 before H4 to maintain proper hierarchy"
    }
  ],
  recommendations: [
    {
      title: "Use only one H1 per page",
      description: "Each page should have exactly one H1 tag that describes the main topic",
      priority: "high",
      category: "Structure"
    },
    {
      title: "Maintain proper heading hierarchy",
      description: "Don't skip heading levels (e.g., don't use H4 directly after H2)",
      priority: "high",
      category: "Hierarchy"
    },
    {
      title: "Make headings descriptive",
      description: "Use clear, descriptive text in headings that accurately represent the content",
      priority: "medium",
      category: "Content"
    }
  ]
}

export function HeadingStructureAudit() {
  const [url, setUrl] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [results, setResults] = useState<HeadingAuditResult | null>(null)

  const handleAudit = async () => {
    if (!url) return
    
    setIsLoading(true)
    setTimeout(() => {
      setResults({ ...mockResults, url })
      setIsLoading(false)
    }, 2500)
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "good": return <CheckCircle className="h-4 w-4 text-green-600" />
      case "warning": return <AlertTriangle className="h-4 w-4 text-yellow-600" />
      case "error": return <XCircle className="h-4 w-4 text-red-600" />
      default: return <AlertTriangle className="h-4 w-4 text-gray-600" />
    }
  }

  const getHeadingIndent = (level: number) => {
    return `ml-${(level - 1) * 4}`
  }

  return (
    <div className="space-y-6">
      {/* URL Input */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Heading Structure Analysis
          </CardTitle>
          <CardDescription>
            Analyze the heading structure (H1-H6) across your website for SEO and accessibility
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4">
            <input
              type="url"
              placeholder="https://example.com"
              value={url}
              onChange={(e) => setUrl(e.target.value)}
              className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <Button 
              onClick={handleAudit} 
              disabled={!url || isLoading}
              className="min-w-[120px]"
            >
              {isLoading ? "Analyzing..." : "Analyze Headings"}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Loading State */}
      {isLoading && (
        <Card>
          <CardContent className="py-8">
            <div className="text-center space-y-4">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
              <p className="text-gray-600">Analyzing heading structure...</p>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Results */}
      {results && !isLoading && (
        <div className="space-y-6">
          {/* Overall Score */}
          <Card>
            <CardHeader>
              <CardTitle>Heading Structure Score</CardTitle>
              <CardDescription>Overall heading structure optimization score</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <div className={`text-4xl font-bold ${results.overallScore >= 80 ? 'text-green-600' : results.overallScore >= 60 ? 'text-yellow-600' : 'text-red-600'}`}>
                    {results.overallScore}
                  </div>
                  <div>
                    <div className="text-sm text-gray-600">Structure Score</div>
                    <div className="text-xs text-gray-500">{results.pages.length} pages analyzed</div>
                  </div>
                </div>
                <FileText className="h-8 w-8 text-gray-400" />
              </div>
            </CardContent>
          </Card>

          {/* Pages Analysis */}
          <Card>
            <CardHeader>
              <CardTitle>Page-by-Page Analysis</CardTitle>
              <CardDescription>Heading structure for each page</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {results.pages.map((page, index) => (
                  <div key={index} className="border rounded-lg p-4">
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="font-medium text-lg">{page.url}</h3>
                      <div className="flex items-center gap-4">
                        <div className="text-center">
                          <div className={`text-lg font-bold ${page.score >= 80 ? 'text-green-600' : page.score >= 60 ? 'text-yellow-600' : 'text-red-600'}`}>
                            {page.score}
                          </div>
                          <div className="text-xs text-gray-600">Score</div>
                        </div>
                        <div className="text-center">
                          <div className={`text-lg font-bold ${page.h1Count === 1 ? 'text-green-600' : 'text-red-600'}`}>
                            {page.h1Count}
                          </div>
                          <div className="text-xs text-gray-600">H1 Tags</div>
                        </div>
                      </div>
                    </div>

                    {/* Heading Hierarchy Visualization */}
                    <div className="mb-4">
                      <h4 className="font-medium text-sm mb-2">Heading Hierarchy</h4>
                      <div className="bg-gray-50 p-3 rounded-lg">
                        {page.headings.map((heading, i) => (
                          <div key={i} className={`flex items-center gap-2 py-1 ${getHeadingIndent(heading.level)}`}>
                            {getStatusIcon(heading.status)}
                            <Badge variant="outline" className="text-xs">
                              H{heading.level}
                            </Badge>
                            <span className="text-sm">{heading.text}</span>
                            {heading.issues.length > 0 && (
                              <span className="text-xs text-red-600">({heading.issues.join(", ")})</span>
                            )}
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Issues Summary */}
                    {(page.skippedLevels.length > 0 || page.missingLevels.length > 0 || page.h1Count !== 1) && (
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                        {page.h1Count !== 1 && (
                          <div className="flex items-center gap-2 text-red-600">
                            <XCircle className="h-4 w-4" />
                            <span>{page.h1Count === 0 ? "No H1 tag" : "Multiple H1 tags"}</span>
                          </div>
                        )}
                        {page.skippedLevels.length > 0 && (
                          <div className="flex items-center gap-2 text-yellow-600">
                            <AlertTriangle className="h-4 w-4" />
                            <span>Skipped levels: H{page.skippedLevels.join(", H")}</span>
                          </div>
                        )}
                        {page.missingLevels.length > 0 && (
                          <div className="flex items-center gap-2 text-red-600">
                            <XCircle className="h-4 w-4" />
                            <span>Missing levels: H{page.missingLevels.join(", H")}</span>
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Issues */}
          <Card>
            <CardHeader>
              <CardTitle>Issues Found</CardTitle>
              <CardDescription>Heading structure issues that need attention</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {results.issues.map((issue, index) => (
                  <div key={index} className="flex items-start gap-3 p-3 border rounded-lg">
                    {issue.type === "error" && <XCircle className="h-5 w-5 text-red-600 mt-0.5" />}
                    {issue.type === "warning" && <AlertTriangle className="h-5 w-5 text-yellow-600 mt-0.5" />}
                    {issue.type === "info" && <CheckCircle className="h-5 w-5 text-blue-600 mt-0.5" />}
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <span className="font-medium">{issue.message}</span>
                        <Badge variant="outline" className="text-xs">{issue.page}</Badge>
                      </div>
                      <div className="text-sm text-gray-600 mb-2">
                        Heading: <code className="bg-gray-100 px-1 rounded">{issue.heading}</code>
                      </div>
                      <div className="text-sm text-blue-600">{issue.recommendation}</div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Recommendations */}
          <Card>
            <CardHeader>
              <CardTitle>Optimization Recommendations</CardTitle>
              <CardDescription>Best practices for heading structure</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {results.recommendations.map((rec, index) => (
                  <div key={index} className="border rounded-lg p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <h4 className="font-medium">{rec.title}</h4>
                          <Badge variant="outline" className="text-xs">{rec.category}</Badge>
                        </div>
                        <p className="text-sm text-gray-600 mb-3">{rec.description}</p>
                        <Badge 
                          variant={rec.priority === "high" ? "destructive" : rec.priority === "medium" ? "secondary" : "outline"}
                          className="text-xs"
                        >
                          {rec.priority} priority
                        </Badge>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Best Practices */}
          <Card>
            <CardHeader>
              <CardTitle>Heading Structure Best Practices</CardTitle>
              <CardDescription>Guidelines for optimal heading structure</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div className="space-y-2">
                  <h4 className="font-medium text-green-600">✓ Do</h4>
                  <ul className="space-y-1 text-gray-600">
                    <li>• Use exactly one H1 per page</li>
                    <li>• Follow logical hierarchy (H1 → H2 → H3)</li>
                    <li>• Make headings descriptive and relevant</li>
                    <li>• Use headings to structure content</li>
                  </ul>
                </div>
                <div className="space-y-2">
                  <h4 className="font-medium text-red-600">✗ Don't</h4>
                  <ul className="space-y-1 text-gray-600">
                    <li>• Skip heading levels (H2 → H4)</li>
                    <li>• Use multiple H1 tags on one page</li>
                    <li>• Use headings for styling only</li>
                    <li>• Make headings too long or vague</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  )
}
