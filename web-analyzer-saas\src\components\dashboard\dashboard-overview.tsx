"use client";

import {
  <PERSON>,
  <PERSON><PERSON>ontent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import {
  BarChart3,
  Globe,
  TrendingUp,
  TrendingDown,
  Activity,
  Users,
  Clock,
  CheckCircle,
  AlertTriangle,
  XCircle,
  ArrowRight,
  Zap,
} from "@/components/ui/icons";
import Link from "next/link";

// Mock data - replace with real data from your API
const stats = [
  {
    title: "Total Websites",
    value: "24",
    change: "+2 this month",
    trend: "up",
    icon: Globe,
  },
  {
    title: "Audits Completed",
    value: "156",
    change: "+12 this week",
    trend: "up",
    icon: CheckCircle,
  },
  {
    title: "Issues Found",
    value: "89",
    change: "-5 from last audit",
    trend: "down",
    icon: Alert<PERSON>riangle,
  },
  {
    title: "AI Credits Used",
    value: "2,340",
    change: "760 remaining",
    trend: "neutral",
    icon: Zap,
  },
];

const recentAudits = [
  {
    id: 1,
    website: "example.com",
    type: "Full Audit",
    status: "completed",
    score: 85,
    issues: 12,
    completedAt: "2 hours ago",
  },
  {
    id: 2,
    website: "mystore.com",
    type: "SEO Audit",
    status: "in-progress",
    score: null,
    issues: null,
    completedAt: null,
  },
  {
    id: 3,
    website: "blog.example.com",
    type: "Performance Audit",
    status: "completed",
    score: 92,
    issues: 3,
    completedAt: "1 day ago",
  },
  {
    id: 4,
    website: "shop.example.com",
    type: "Full Audit",
    status: "failed",
    score: null,
    issues: null,
    completedAt: "2 days ago",
  },
];

const quickActions = [
  {
    title: "New Website Audit",
    description: "Start a comprehensive audit of a new website",
    href: "/audit/new",
    icon: Activity,
    color: "bg-blue-500",
  },
  {
    title: "Competitor Analysis",
    description: "Compare your site against competitors",
    href: "/competitor/new",
    icon: Users,
    color: "bg-green-500",
  },
  {
    title: "AI Content Analysis",
    description: "Get AI-powered insights on your content",
    href: "/ai/content-analysis",
    icon: Zap,
    color: "bg-purple-500",
  },
  {
    title: "Generate Report",
    description: "Create a white-label PDF report",
    href: "/reports/new",
    icon: BarChart3,
    color: "bg-orange-500",
  },
];

export function DashboardOverview() {
  return (
    <div className="space-y-6">
      {/* Stats Grid */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {stats.map((stat) => (
          <Card key={stat.title}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {stat.title}
              </CardTitle>
              <stat.icon className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stat.value}</div>
              <p
                className={`text-xs ${
                  stat.trend === "up"
                    ? "text-green-600"
                    : stat.trend === "down"
                    ? "text-red-600"
                    : "text-muted-foreground"
                }`}
              >
                {stat.trend === "up" && (
                  <TrendingUp className="inline h-3 w-3 mr-1" />
                )}
                {stat.trend === "down" && (
                  <TrendingDown className="inline h-3 w-3 mr-1" />
                )}
                {stat.change}
              </p>
            </CardContent>
          </Card>
        ))}
      </div>

      <div className="grid gap-6 lg:grid-cols-3">
        {/* Recent Audits */}
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle>Recent Audits</CardTitle>
            <CardDescription>
              Your latest website audits and their results
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentAudits.map((audit) => (
                <div
                  key={audit.id}
                  className="flex items-center justify-between p-4 border rounded-lg"
                >
                  <div className="flex items-center space-x-4">
                    <div className="flex-1">
                      <div className="flex items-center gap-2">
                        <h4 className="font-medium">{audit.website}</h4>
                        <Badge variant="outline">{audit.type}</Badge>
                      </div>
                      <div className="flex items-center gap-4 mt-1 text-sm text-muted-foreground">
                        <span className="flex items-center gap-1">
                          {audit.status === "completed" && (
                            <CheckCircle className="h-3 w-3 text-green-500" />
                          )}
                          {audit.status === "in-progress" && (
                            <Clock className="h-3 w-3 text-yellow-500" />
                          )}
                          {audit.status === "failed" && (
                            <XCircle className="h-3 w-3 text-red-500" />
                          )}
                          {audit.status}
                        </span>
                        {audit.completedAt && <span>{audit.completedAt}</span>}
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-4">
                    {audit.score && (
                      <div className="text-right">
                        <div className="text-lg font-semibold">
                          {audit.score}
                        </div>
                        <div className="text-xs text-muted-foreground">
                          Score
                        </div>
                      </div>
                    )}
                    {audit.issues !== null && (
                      <div className="text-right">
                        <div className="text-lg font-semibold text-orange-600">
                          {audit.issues}
                        </div>
                        <div className="text-xs text-muted-foreground">
                          Issues
                        </div>
                      </div>
                    )}
                    <Button variant="ghost" size="sm">
                      <ArrowRight className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
            <div className="mt-4">
              <Button variant="outline" className="w-full">
                View All Audits
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
            <CardDescription>Start your next analysis</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {quickActions.map((action) => (
                <Link key={action.title} href={action.href}>
                  <div className="flex items-center gap-3 p-3 rounded-lg border hover:bg-accent transition-colors cursor-pointer">
                    <div className={`p-2 rounded-md ${action.color}`}>
                      <action.icon className="h-4 w-4 text-white" />
                    </div>
                    <div className="flex-1">
                      <div className="font-medium text-sm">{action.title}</div>
                      <div className="text-xs text-muted-foreground">
                        {action.description}
                      </div>
                    </div>
                  </div>
                </Link>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Usage Overview */}
      <Card>
        <CardHeader>
          <CardTitle>Monthly Usage</CardTitle>
          <CardDescription>Your current plan usage and limits</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-6 md:grid-cols-3">
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Website Audits</span>
                <span>24 / 50</span>
              </div>
              <Progress value={48} className="h-2" />
            </div>
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>AI Credits</span>
                <span>2,340 / 5,000</span>
              </div>
              <Progress value={47} className="h-2" />
            </div>
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Team Members</span>
                <span>3 / 10</span>
              </div>
              <Progress value={30} className="h-2" />
            </div>
          </div>
          <div className="mt-4 flex justify-between items-center">
            <p className="text-sm text-muted-foreground">
              Pro Plan • Resets in 12 days
            </p>
            <Button variant="outline" size="sm">
              Upgrade Plan
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
