["Iterator", "Iterator.concat", "Iterator.from", "Iterator.zip", "Iterator.zipKeyed", "Iterator.prototype", "Iterator.prototype.constructor", "Iterator.prototype.drop", "Iterator.prototype.every", "Iterator.prototype.filter", "Iterator.prototype.find", "Iterator.prototype.flatMap", "Iterator.prototype.forEach", "Iterator.prototype.map", "Iterator.prototype.reduce", "Iterator.prototype.some", "Iterator.prototype.take", "Iterator.prototype.toArray"]