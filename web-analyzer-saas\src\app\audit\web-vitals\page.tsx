import { AppLayout } from "@/components/layout/app-layout"
import { WebVitalsAudit } from "@/components/audit/web-vitals-audit"

export default function WebVitalsPage() {
  return (
    <AppLayout>
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Core Web Vitals Audit</h1>
          <p className="text-gray-600">
            Measure CLS, FCP, LCP, TTI metrics for performance health and user experience optimization.
          </p>
        </div>
        <WebVitalsAudit />
      </div>
    </AppLayout>
  )
}
