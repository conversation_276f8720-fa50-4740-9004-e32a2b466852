# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [v1.1.8](https://github.com/es-shims/Function.prototype.name/compare/v1.1.7...v1.1.8) - 2024-12-19

### Commits

- [actions] split out node 10-20, and 20+ [`b5ea555`](https://github.com/es-shims/Function.prototype.name/commit/b5ea555b2a4db8eb531bccb4d6b2c916de9b8089)
- [Refactor] use `call-bound` directly [`f6a6c64`](https://github.com/es-shims/Function.prototype.name/commit/f6a6c640f7f209c3fc1ea65c0eb31e622e2c9399)

## [v1.1.7](https://github.com/es-shims/Function.prototype.name/compare/v1.1.6...v1.1.7) - 2024-12-15

### Commits

- [actions] split out node 10-20, and 20+ [`47155b0`](https://github.com/es-shims/Function.prototype.name/commit/47155b0bef19c37e5cf9dfff393bc14c6079959a)
- [Refactor] use `hasown` and `is-callable` directly, instead of `es-abstract` [`d5118d6`](https://github.com/es-shims/Function.prototype.name/commit/d5118d65f89f8af4d1109943955ba65f88f4aa6b)
- [Deps] update `call-bind`, `define-properties`, `es-abstract` [`cfa8b2e`](https://github.com/es-shims/Function.prototype.name/commit/cfa8b2e4fcfa437dde6436c2c34e3eb4bb646907)
- [Dev Deps] update `@es-shims/api`, `@ljharb/eslint-config`, `auto-changelog`, `npmignore`, `tape` [`2077d9a`](https://github.com/es-shims/Function.prototype.name/commit/2077d9a9039c7cbf78816188bf486560366437bc)
- [Tests] replace `aud` with `npm audit` [`219e0a4`](https://github.com/es-shims/Function.prototype.name/commit/219e0a43a3de2d96e78ec61a43ccb61be40d8da8)
- [Dev Deps] add missing peer dep [`0b16b2b`](https://github.com/es-shims/Function.prototype.name/commit/0b16b2b1013b3c92793bcf87c573eab356e00388)

## [v1.1.6](https://github.com/es-shims/Function.prototype.name/compare/v1.1.5...v1.1.6) - 2023-08-28

### Commits

- [actions] reuse common workflows [`5f6bfba`](https://github.com/es-shims/Function.prototype.name/commit/5f6bfba9d2c42fbac8f4812396bc71f79464846c)
- [meta] use `npmignore` to autogenerate an npmignore file [`28ea2f9`](https://github.com/es-shims/Function.prototype.name/commit/28ea2f9a9dd48623cba04e94c491033f1c9d1e90)
- [Fix] properly recognize `document.all` in IE 6-8 [`316d676`](https://github.com/es-shims/Function.prototype.name/commit/316d67641d54bf221ed5edfdb9e04af3b98caad8)
- [Fix] only return an own `name` [`d647609`](https://github.com/es-shims/Function.prototype.name/commit/d6476090e110733b52a922f4d0dbfdbc9478c653)
- [Tests] add browserstack browser tests [`67ae402`](https://github.com/es-shims/Function.prototype.name/commit/67ae402aabcad83df2f7d7e356d059a84fe71f44)
- [meta] better `eccheck` command [`728df4c`](https://github.com/es-shims/Function.prototype.name/commit/728df4cc81a51a131a36c0768c4adb7668ad7569)
- [meta] add `auto-changelog` [`dbb700b`](https://github.com/es-shims/Function.prototype.name/commit/dbb700b38ef4c18e0ce0670a2ffface9ffd251a0)
- [readme] fix eclint [`c98fdf1`](https://github.com/es-shims/Function.prototype.name/commit/c98fdf1bc5451de667945c41187a67022f750001)
- [readme] add tested browsers [`d41325c`](https://github.com/es-shims/Function.prototype.name/commit/d41325ceec61627f63281d0649e4e0004f3e0609)
- [actions] update rebase action to use reusable workflow [`085f340`](https://github.com/es-shims/Function.prototype.name/commit/085f3400785cd4f3fb762b73b095f5dfb795a0b3)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `@es-shims/api`, `safe-publish-latest`, `tape` [`3f071ce`](https://github.com/es-shims/Function.prototype.name/commit/3f071cef2e1feebfd7d0daea7d6392c2feada091)
- [actions] update codecov uploader [`a187b4f`](https://github.com/es-shims/Function.prototype.name/commit/a187b4fd07dbbeee12e8dc60651f122ab3f41f8d)
- [Deps] update `define-properties`, `es-abstract` [`3ca42ef`](https://github.com/es-shims/Function.prototype.name/commit/3ca42ef76d5d4016d1ea87d806dc7e4a09d9b4f8)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `tape` [`8de25d2`](https://github.com/es-shims/Function.prototype.name/commit/8de25d2b9b523bd385b0bf3bb9213c11ecf8f1ba)
- [Dev Deps] update `@es-shims/api`, `@ljharb/eslint-config`, `aud`, `tape` [`8b04da7`](https://github.com/es-shims/Function.prototype.name/commit/8b04da71695a1b9cf285ee926ffeec55b543595b)
- [Dev Deps] update `@ljharb/eslint-config`, `aud`, `tape` [`39d8538`](https://github.com/es-shims/Function.prototype.name/commit/39d853854136a749c94e10f9fb06ba73903671a6)
- [meta] reorder scripts [`054f96b`](https://github.com/es-shims/Function.prototype.name/commit/054f96b5e88e08e65c4e27bcb799c7cea2bc3462)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `tape` [`bebee89`](https://github.com/es-shims/Function.prototype.name/commit/bebee894a7989aef6e9db0dc8b16cbd9134b629c)
- [Dev Deps] update `aud`, `tape` [`8e68159`](https://github.com/es-shims/Function.prototype.name/commit/8e681599b4fbf26e921f61fd603da0524369b72c)
- [Tests] handle Function.prototype in Opera 12.1 [`f3b8f9a`](https://github.com/es-shims/Function.prototype.name/commit/f3b8f9a40b88f6da5ad41b874c3f2acf6fb30378)
- [Deps] update `es-abstract`, `functions-have-names` [`6a59889`](https://github.com/es-shims/Function.prototype.name/commit/6a598893f013182070479a8cc52afd44e556561f)
- [Deps] update `define-properties`, `es-abstract` [`cd1c5e7`](https://github.com/es-shims/Function.prototype.name/commit/cd1c5e773c3740ec563a26e657d764aba7c35a8c)
- [Deps] update `es-abstract` [`3584585`](https://github.com/es-shims/Function.prototype.name/commit/35845851109f767e3bc84ebef989ca93e5851276)
- [Deps] update `es-abstract` [`0e2f6d9`](https://github.com/es-shims/Function.prototype.name/commit/0e2f6d99d554a8b6b7c835702c8408832f9a2684)
- [Deps] update `es-abstract` [`b11748e`](https://github.com/es-shims/Function.prototype.name/commit/b11748ebbda2d840ac625ae6627cfdb090b94434)
- [Dev Deps] update `tape` [`d787a81`](https://github.com/es-shims/Function.prototype.name/commit/d787a81a1e1ce6d00dda6272e93a43bb193b1286)
- [Deps] update `es-abstract` [`4692639`](https://github.com/es-shims/Function.prototype.name/commit/469263915b07db8342f0aad29ad7eba083bea277)
- [Dev Deps] add `in-publish` [`568e263`](https://github.com/es-shims/Function.prototype.name/commit/568e2635099de326768f40d9e0eacbd024861676)

<!-- auto-changelog-above -->
1.1.5 / 2021-10-01
=================
  * [Deps] update `es-abstract`
  * [meta] use `prepublishOnly` script for npm 7+
  * [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `@es-shims/api`, `aud`, `tape`
  * [actions] update workflows
  * [actions] use `node/install` instead of `node/run`; use `codecov` action

1.1.4 / 2021-02-22
=================
  * [readme] remove travis badge
  * [meta] remove audit-level
  * [meta] gitignore coverage output
  * [meta] do not publish github action workflow files
  * [Deps] update `call-bind`, `es-abstract`, `functions-have-names`
  * [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `has-strict-mode`, `tape`
  * [Tests] increase coverage
  * [actions] update workflows

1.1.3 / 2020-11-27
=================
  * [Deps] update `es-abstract`, `functions-have-names`; use `call-bind` where applicable
  * [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `tape`, `make-arrow-function`, `make-generator-function`; add `aud`, `make-async-function`
  * [actions] add "Allow Edits" workflow
  * [actions] switch Automatic Rebase workflow to `pull_request_target` event
  * [Tests] migrate tests to Github Actions
  * [Tests] run `nyc` on all tests
  * [Tests] add `implementation` test; run `es-shim-api` in postlint; use `tape` runner
  * [Tests] only audit prod deps

1.1.2 / 2019-12-14
=================
  * [Refactor] use `es-abstract`
  * [Deps] update `functions-have-names`
  * [meta] add `funding` field
  * [meta] fix repo capitalization
  * [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `safe-publish-latest`
  * [Tests] use shared travis-ci configs
  * [actions] add automatic rebasing / merge commit blocking

1.1.1 / 2019-07-24
=================
  * [Refactor] use `functions-have-names`
  * [meta] clean up package.json scripts
  * [meta] update links
  * [meta] create FUNDING.yml
  * [Deps] update `is-callable`, `define-properties`
  * [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `tape`, `safe-publish-latest`,  `covert`
  * [Tests] use `eccheck` over `editorconfig-tools`
  * [Tests] use `npx aud` instead of `nsp` or `npm audit` with hoops
  * [Tests] up to `node` `v11.7`, `v10.15`, `v9.11`, `v8.15`, `v6.16`, `v4.9`
  * [Test] remove `jscs`

1.1.0 / 2017-12-31
=================
  * [New] add `auto` entry point
  * [Deps] update `function-bind`
  * [Dev Deps] update `uglify-register`, `tape`, `nsp`, `eslint`, `@ljharb/eslint-config`, `@es-shims/api`
  * [Tests] up to `node` `v9.3`, `v8.9`, `v6.12`; use `nvm install-latest-npm`; pin included builds to LTS

1.0.3 / 2017-07-21
=================
  * [Fix] be robust against function name mangling
  * [Refactor] move function name detection to separate file

1.0.2 / 2017-07-14
=================
  * [Refactor] shim: Remove unnecessary `!functionsHaveNames` check

1.0.1 / 2017-07-11
=================
  * [Fix] in IE 9-11, we must rely on `.call` being available (#13)
  * [Fix] ensure that `Function.prototype.name` does not erase the getter
  * [Deps] update `is-callable`
  * [Dev Deps] add `safe-publish-latest`
  * [Dev Deps] update `tape`, `jscs`, `nsp`, `eslint`, `@ljharb/eslint-config`, `@es-shims/api`
  * [Tests] up to `node` `v8.1`; `v7.10`, `v6.11`, `v4.8`; improve matrix; newer npm fails on older nodes
  * [Tests] use `Object` to avoid function name inference in node 7

1.0.0 / 2016-02-27
=================
  * Initial release.
